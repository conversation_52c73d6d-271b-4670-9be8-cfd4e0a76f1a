import aiofiles
import asyncio
import logging

# Daftar encoding yang didukung untuk output
SUPPORTED_OUTPUT_ENCODINGS = [
    'utf-8',
    'utf-8-sig',
    'utf-16',
    'utf-16-le',
    'utf-16-be',
    'utf-32',
    'utf-32-le',
    'utf-32-be'
]

def create_vcf_content(contact_names, numbers):
    """Create vcf content from contact names and numbers."""
    vcf_entries = []
    for name, number in zip(contact_names, numbers):
        vcf_entries.append(
            f"BEGIN:VCARD\nVERSION:3.0\nFN:{name}\nTEL;TYPE=CELL:{number}\nEND:VCARD"
        )
    return "\n".join(vcf_entries)

async def write_vcf_file(output_path, vcf_content, encoding="utf-8", max_retry=3, delay=2):
    """Write vcf content to file asynchronously dengan retry dan pilihan encoding."""
    # Validasi encoding
    if encoding not in SUPPORTED_OUTPUT_ENCODINGS:
        logging.warning(f"Encoding {encoding} tidak didukung, menggunakan utf-8")
        encoding = "utf-8"

    for attempt in range(1, max_retry + 1):
        try:
            async with aiofiles.open(output_path, "w", encoding=encoding) as f:
                await f.write(vcf_content)
            # Log hanya untuk debug, tidak untuk info
            # logging.info(f"VCF file {output_path} berhasil ditulis dengan encoding: {encoding}")
            return True
        except Exception as e:
            logging.error(f"Error writing vcf: {e} (percobaan {attempt})")
            if attempt == max_retry:
                raise
            await asyncio.sleep(delay)

async def write_text_file(output_path, content, encoding="utf-8", max_retry=3, delay=2):
    """Write text content to file asynchronously dengan retry dan pilihan encoding."""
    # Validasi encoding
    if encoding not in SUPPORTED_OUTPUT_ENCODINGS:
        logging.warning(f"Encoding {encoding} tidak didukung, menggunakan utf-8")
        encoding = "utf-8"

    for attempt in range(1, max_retry + 1):
        try:
            async with aiofiles.open(output_path, "w", encoding=encoding) as f:
                await f.write(content)
            # logging.info(f"Text file {output_path} berhasil ditulis dengan encoding: {encoding}")  # Reduced logging
            return True
        except Exception as e:
            logging.error(f"Error writing text file: {e} (percobaan {attempt})")
            if attempt == max_retry:
                raise
            await asyncio.sleep(delay)