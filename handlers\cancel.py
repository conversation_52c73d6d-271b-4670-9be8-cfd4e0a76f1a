"""
Handler untuk cancel command
"""

import logging
from aiogram import Router, types, F
from aiogram.filters import Command
from aiogram.fsm.context import FSMContext
from utils.user_isolation import is_user_in_sending_mode, set_user_cancel_flag
# restore_normal_keyboard sudah tidak dipakai lagi

router = Router()

def log_user(message: types.Message):
    # logging.info(f"user: {message.text}")  # Disabled
    pass

def log_bot(text: str):
    # logging.info(f"bot: {text}")  # Disabled
    pass

@router.message(Command("cancel"), F.chat.type == "private")
async def cancel_handler(message: types.Message, state: FSMContext):
    """
    Handler untuk command /cancel.
    Hanya berfungsi saat user dalam sending mode.
    """
    log_user(message)
    user_id = message.from_user.id
    
    try:
        # Check apakah user sedang dalam sending mode
        if not is_user_in_sending_mode(user_id):
            # User tidak dalam sending mode - silent ignore
            return

        # User dalam sending mode - set cancel flag
        await set_user_cancel_flag(user_id, True)

        # Tidak ada respon langsung - pesan cancel akan muncul dari send_files_with_sending_mode
        
        # Log cancel action
        logging.info(f"User {user_id} cancelled file sending")
        
        # Note: Keyboard akan di-restore oleh send_files_with_sending_mode
        # setelah pengiriman benar-benar berhenti
        
    except Exception as e:
        logging.error(f"Error in cancel handler for user {user_id}: {e}")
        # Fallback response
        try:
            await message.answer("❌ Terjadi kesalahan saat membatalkan pengiriman.")
        except:
            pass  # Silent fail
