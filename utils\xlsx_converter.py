"""
XLSX Converter - Convert VCF/TXT to Excel format
"""

import logging
import os
import re
from typing import List, Dict, Any
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter

async def extract_numbers_for_xlsx(file_path: str) -> List[str]:
    """
    Extract nomor telepon dari file menggunakan logic yang sama dengan to_txt

    Args:
        file_path: Path ke file

    Returns:
        List[str]: List nomor telepon yang valid
    """
    try:
        # Import extract_numbers dari utils.file (sama dengan yang dipakai to_txt)
        from utils.file import extract_numbers
        numbers = await extract_numbers(file_path)
        return numbers if numbers else []
    except Exception as e:
        logging.error(f"Error extracting numbers for XLSX: {e}")
        return []

def prepare_phone_numbers(numbers: List[str]) -> List[str]:
    """
    Prepare list of phone numbers untuk Excel (simple list)

    Args:
        numbers: List nomor telepon

    Returns:
        List[str]: List nomor telepon yang sudah dibersihkan
    """
    clean_numbers = []

    for number in numbers:
        if number.strip():  # Skip empty numbers
            clean_numbers.append(number.strip())

    return clean_numbers

# Function ini sudah tidak dipakai - diganti dengan extract_numbers_for_xlsx

def create_excel_file(phone_numbers: List[str], output_path: str) -> bool:
    """
    Create Excel file dengan simple list nomor telepon

    Args:
        phone_numbers: List nomor telepon
        output_path: Path untuk save Excel file

    Returns:
        bool: True jika berhasil
    """
    try:
        # Create workbook dan worksheet
        wb = Workbook()
        ws = wb.active
        ws.title = "Phone Numbers"

        # Write phone numbers langsung ke kolom A (no header)
        for row, phone_number in enumerate(phone_numbers, 1):
            ws.cell(row=row, column=1, value=phone_number)

        # Set column width untuk nomor telepon
        ws.column_dimensions['A'].width = 20

        # Save file
        wb.save(output_path)
        # logging.info(f"Excel file created successfully: {output_path}")  # Reduced logging
        return True

    except Exception as e:
        logging.error(f"Error creating Excel file: {e}")
        return False

async def convert_to_xlsx(input_file_path: str, output_file_path: str) -> bool:
    """
    Convert file ke XLSX menggunakan logic yang sama dengan to_txt

    Args:
        input_file_path: Path ke input file
        output_file_path: Path untuk output XLSX file

    Returns:
        bool: True jika berhasil
    """
    try:
        # Extract numbers menggunakan logic yang sama dengan to_txt
        numbers = await extract_numbers_for_xlsx(input_file_path)

        if not numbers:
            logging.warning("No valid phone numbers found in file")
            return False

        # Prepare phone numbers untuk Excel (simple list)
        clean_numbers = prepare_phone_numbers(numbers)

        if not clean_numbers:
            logging.warning("No clean phone numbers to save")
            return False

        # Create Excel file dengan simple phone list
        success = create_excel_file(clean_numbers, output_file_path)

        # if success:
        #     logging.info(f"Successfully converted {len(clean_numbers)} phone numbers to Excel")  # Reduced logging

        return success

    except Exception as e:
        logging.error(f"Error converting file to XLSX: {e}")
        return False

async def get_contact_count_from_file(file_path: str) -> int:
    """
    Get jumlah kontak dari file menggunakan logic yang sama dengan to_txt

    Args:
        file_path: Path ke file

    Returns:
        int: Jumlah kontak
    """
    try:
        # Extract numbers menggunakan logic yang sama dengan to_txt
        numbers = await extract_numbers_for_xlsx(file_path)
        return len(numbers) if numbers else 0

    except Exception as e:
        logging.error(f"Error counting contacts: {e}")
        return 0
