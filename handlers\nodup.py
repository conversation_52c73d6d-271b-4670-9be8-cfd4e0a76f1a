import logging
import os
import time
import aiofiles
import asyncio
from aiogram import Router, types, F, Bot
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from aiogram.filters import Command
from utils.retry_send import retry_send_document
from utils.user_isolation import with_user_isolation, is_user_busy
from utils.user_directories import get_user_file_path
from utils.smart_logging import log_user_smart, log_bot, flush_user_logs

router = Router()

class NodupStates(StatesGroup):
    waiting_files = State()
    waiting_done = State()

# Gunakan smart logging dari utils
def log_user(message: types.Message):
    log_user_smart(message)

def clean_and_validate_number(line):
    import re
    line = line.strip()
    if not line:
        return None
    if line.startswith("+"):
        nomor = "+" + re.sub(r"[^\d]", "", line[1:])
    else:
        nomor = re.sub(r"[^\d]", "", line)
        nomor = "+" + nomor
    digit_count = len(re.sub(r"[^\d]", "", nomor))
    if digit_count < 8:
        return None
    return nomor

async def extract_numbers_from_vcf(file_path):
    numbers = []
    vcards = []
    async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
        content = await f.read()
    cards = content.split("BEGIN:VCARD")
    for card in cards:
        card = card.strip()
        if not card:
            continue
        if not card.startswith("BEGIN:VCARD"):
            card = "BEGIN:VCARD\n" + card
        tel_lines = [line for line in card.splitlines() if line.strip().startswith("TEL")]
        nomor = None
        for line in tel_lines:
            nomor_raw = line.split(":")[-1].strip()
            nomor = clean_and_validate_number(nomor_raw)
            if nomor:
                break
        vcards.append((card, nomor))
        numbers.append(nomor)
    return vcards

async def extract_numbers_from_file(file_path, ext):
    numbers = []
    if ext == ".vcf":
        vcards = await extract_numbers_from_vcf(file_path)
        numbers = [n for _, n in vcards if n]
    elif ext in [".txt", ".csv"]:
        async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
            lines = await f.readlines()
        for line in lines:
            nomor = clean_and_validate_number(line)
            if nomor:
                numbers.append(nomor)
    elif ext in [".xlsx", ".xls"]:
        import pandas as pd
        df = pd.read_excel(file_path)
        for col in df.columns:
            for val in df[col]:
                nomor = clean_and_validate_number(str(val))
                if nomor:
                    numbers.append(nomor)
    else:
        async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
            lines = await f.readlines()
        for line in lines:
            nomor = clean_and_validate_number(line)
            if nomor:
                numbers.append(nomor)
    return numbers

# Handler global untuk perintah lain agar bisa membatalkan proses ini
@router.message(Command("start"), F.chat.type == "private")
async def start_from_nodup(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import start_handler
    await start_handler(message, state)

@router.message(Command("to_vcf"), F.chat.type == "private")
async def to_vcf_from_nodup(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_vcf import to_vcf_start
    await to_vcf_start(message, state)

@router.message(Command("to_txt"), F.chat.type == "private")
async def to_txt_from_nodup(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_txt import to_txt_start
    await to_txt_start(message, state)

@router.message(Command("admin"), F.chat.type == "private")
async def admin_from_nodup(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.admin import admin_start
    await admin_start(message, state)

@router.message(Command("manual"), F.chat.type == "private")
async def manual_from_nodup(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.manual import manual_start
    await manual_start(message, state)

@router.message(Command("add"), F.chat.type == "private")
async def add_from_nodup(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.add import add_start
    await add_start(message, state)

@router.message(Command("delete"), F.chat.type == "private")
async def delete_from_nodup(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.delete import delete_start
    await delete_start(message, state)

@router.message(Command("renamectc"), F.chat.type == "private")
async def renamectc_from_nodup(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamectc import renamectc_start
    await renamectc_start(message, state)

@router.message(Command("renamefile"), F.chat.type == "private")
async def renamefile_from_nodup(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamefile import renamefile_start
    await renamefile_start(message, state)

@router.message(Command("merge"), F.chat.type == "private")
async def merge_from_nodup(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.merge import merge_start
    await merge_start(message, state)

@router.message(Command("split"), F.chat.type == "private")
async def split_from_nodup(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.split import split_start
    await split_start(message, state)

@router.message(Command("count"), F.chat.type == "private")
async def count_from_nodup(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.count import count_start
    await count_start(message, state)

@router.message(Command("getname"), F.chat.type == "private")
async def getname_from_nodup(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.getname import getname_start
    await getname_start(message, state)

@router.message(Command("generate"), F.chat.type == "private")
async def generate_from_nodup(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.generate import generate_start
    await generate_start(message, state)

@router.message(Command("setting"), F.chat.type == "private")
async def setting_from_nodup(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.hide_menu import hide_menu_start
    await hide_menu_start(message, state)

@router.message(Command("help"), F.chat.type == "private")
async def help_from_nodup(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import help_handler
    await help_handler(message, state)

@router.message(Command("nodup"), F.chat.type == "private")
async def nodup_start(message: types.Message, state: FSMContext):
    await state.clear()

    # Silent force release semaphore jika user stuck
    if is_user_busy(message.from_user.id):
        from utils.user_isolation import force_release_user_lock
        force_release_user_lock(message.from_user.id)
        await state.clear()
        # Lanjut ke processing normal tanpa pesan pembatalan

    log_user(message)

    # Flush any pending logs dari command sebelumnya
    flush_user_logs(message.from_user.id)

    bot_msg = "📎 Kirim file yang mau dihapus nomor duplikatnya."
    await message.answer(bot_msg)
    log_bot(bot_msg)
    await state.set_state(NodupStates.waiting_files)
    await state.update_data(files=[], logs=[], file_error=False)

@router.message(NodupStates.waiting_files, F.document, F.chat.type == "private")
async def nodup_receive_file(message: types.Message, state: FSMContext, bot: Bot):
    log_user(message)
    file = message.document
    _, ext = os.path.splitext(file.file_name.lower())
    allowed_ext = [".txt", ".xlsx", ".xls", ".vcf", ".csv"]
    data = await state.get_data()
    if data.get("file_error"):
        return
    if ext not in allowed_ext:
        await state.update_data(files=[], logs=[], file_error=True)
        bot_msg = "❌ Format file tidak didukung!\nUlangi dengan /nodup"
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return
    try:
        files = data.get("files", [])
        logs = data.get("logs", [])
        filename, ext_real = os.path.splitext(file.file_name)
        timestamp = int(time.time() * 1000)
        unique_name = f"{filename}_{timestamp}{ext_real}"
        file_path = get_user_file_path(message.from_user.id, unique_name)
        await bot.download(file, destination=file_path)
        files.append((file_path, file.file_name, message.message_id))
        logs.append((message.message_id, f"bot: File {file.file_name} diterima"))
        await state.update_data(files=files, logs=logs)
        state_now = await state.get_data()
        if len(state_now.get("files", [])) == 1 and not state_now.get("file_error"):
            bot_msg = "✅ File diterima. Ketik /done jika sudah."
            await message.answer(bot_msg)
            log_bot(bot_msg)
    except Exception as e:
        err_msg = "⚠️ Gagal menerima file. Coba lagi."
        log_bot(err_msg)
        logging.error(f"user: kirim file {file.file_name if 'file' in locals() else '[unknown]'} error: {e}")
        await message.answer(err_msg)

@router.message(NodupStates.waiting_files, Command("done"), F.chat.type == "private")
async def nodup_done(message: types.Message, state: FSMContext):
    log_user(message)

    # Flush pending file upload logs sebelum proses
    flush_user_logs(message.from_user.id)

    data = await state.get_data()
    files = data.get("files", [])
    logs = data.get("logs", [])
    if not files:
        bot_msg = "⚠️ Belum ada file. Kirim file dulu."
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return
    files = sorted(files, key=lambda x: x[2])
    logs = sorted(logs, key=lambda x: x[0])
    await state.update_data(files=files, logs=logs)

    # Summary log untuk file reception
    total_files = len(files)
    if total_files > 0:
        logging.info(f"bot: {total_files} file diterima untuk nodup")

    await process_nodup_with_isolation(message, state, files)

async def process_nodup_with_isolation(message, state, files):
    """
    Wrapper function untuk process_nodup dengan user isolation.
    """
    from aiogram.exceptions import TelegramRetryAfter
    user_id = message.from_user.id

    # Jalankan process_nodup dengan user isolation
    success, result = await with_user_isolation(user_id, process_nodup, message, state, files)

    if not success:
        # User sedang busy
        bot_msg = f"⏳ {result}"
        try:
            await message.answer(bot_msg)
            log_bot(bot_msg)
        except TelegramRetryAfter:
            log_bot(bot_msg)

async def process_nodup(message, state, files):
    """
    Fungsi utama untuk memproses nodup.
    """
    total_dupes = 0
    file_paths_to_delete = []

    # Track apakah ada duplikat yang ditemukan di file manapun
    found_any_duplicate = False
    should_send_files = False

    try:
        for file_path, original_filename, _ in files:
            _, ext = os.path.splitext(original_filename.lower())
            if ext == ".vcf":
                vcards = await extract_numbers_from_vcf(file_path)
                seen = set()
                new_vcards = []
                dupes = 0
                for card, nomor in vcards:
                    if nomor and nomor not in seen:
                        seen.add(nomor)
                        new_vcards.append(card)
                    elif nomor:
                        dupes += 1
                total_dupes += dupes
                # Track jika ada duplikat ditemukan
                if dupes > 0:
                    found_any_duplicate = True
                # Selalu buat file output (baik ada duplikat atau tidak)
                output_path = get_user_file_path(message.from_user.id, original_filename)
                async with aiofiles.open(output_path, "w", encoding="utf-8") as f:
                    await f.write("\n".join(new_vcards))
                file_paths_to_delete.append(output_path)
            elif ext in [".txt", ".csv"]:
                numbers = await extract_numbers_from_file(file_path, ext)
                seen = set()
                new_numbers = []
                dupes = 0
                for nomor in numbers:
                    if nomor not in seen:
                        seen.add(nomor)
                        new_numbers.append(nomor)
                    else:
                        dupes += 1
                total_dupes += dupes
                # Track jika ada duplikat ditemukan
                if dupes > 0:
                    found_any_duplicate = True
                # Selalu buat file output (baik ada duplikat atau tidak)
                output_path = get_user_file_path(message.from_user.id, original_filename)
                async with aiofiles.open(output_path, "w", encoding="utf-8") as f:
                    await f.write("\n".join(new_numbers))
                file_paths_to_delete.append(output_path)
            elif ext in [".xlsx", ".xls"]:
                import pandas as pd
                numbers = await extract_numbers_from_file(file_path, ext)
                seen = set()
                new_numbers = []
                dupes = 0
                for nomor in numbers:
                    if nomor not in seen:
                        seen.add(nomor)
                        new_numbers.append(nomor)
                    else:
                        dupes += 1
                total_dupes += dupes
                # Track jika ada duplikat ditemukan
                if dupes > 0:
                    found_any_duplicate = True
                # Selalu buat file output (baik ada duplikat atau tidak)
                output_path = get_user_file_path(message.from_user.id, original_filename)
                df = pd.DataFrame({"Nomor": new_numbers})
                df.to_excel(output_path, index=False)
                file_paths_to_delete.append(output_path)
            else:
                numbers = await extract_numbers_from_file(file_path, ext)
                seen = set()
                new_numbers = []
                dupes = 0
                for nomor in numbers:
                    if nomor not in seen:
                        seen.add(nomor)
                        new_numbers.append(nomor)
                    else:
                        dupes += 1
                total_dupes += dupes
                # Track jika ada duplikat ditemukan
                if dupes > 0:
                    found_any_duplicate = True
                # Selalu buat file output (baik ada duplikat atau tidak)
                output_path = get_user_file_path(message.from_user.id, original_filename)
                async with aiofiles.open(output_path, "w", encoding="utf-8") as f:
                    await f.write("\n".join(new_numbers))
                file_paths_to_delete.append(output_path)

        # Cek apakah duplikat ditemukan di file manapun
        if not found_any_duplicate:
            # Jika tidak ada duplikat yang ditemukan, kirim pesan error dan jangan kirim file
            bot_msg = "❌ Tidak ada nomor duplikat ditemukan di file manapun"
            await message.answer(bot_msg)
            log_bot(bot_msg)
            should_send_files = False
        else:
            # Jika ada duplikat yang ditemukan, kirim semua file
            for file_path, original_filename, _ in files:
                await retry_send_document(message, get_user_file_path(message.from_user.id, original_filename), original_filename)
                # log_bot(f"kirim file {original_filename}")  # Dikurangi untuk mengurangi spam log
            bot_msg = f"🗑️ {total_dupes} nomor duplikat ditemukan dan dihapus\n📤 File berhasil dikirim!"
            await message.answer(bot_msg)
            log_bot(bot_msg)
            should_send_files = True
    except Exception as e:
        err_msg = f"❌ Gagal hapus duplikat. Ulangi dengan /nodup\n{e}"
        logging.error(err_msg)
        log_bot(err_msg)
        await message.answer(err_msg)
    finally:
        # Hanya hapus file hasil jika file sudah dikirim
        if should_send_files:
            async def remove_file(path):
                try:
                    if os.path.exists(path):
                        os.remove(path)
                        # logging.info(f"File hasil dihapus: {path}")
                except Exception as e:
                    logging.error(f"Gagal hapus file hasil: {path} ({e})")
            await asyncio.gather(*(remove_file(path) for path in file_paths_to_delete))
        await state.clear()