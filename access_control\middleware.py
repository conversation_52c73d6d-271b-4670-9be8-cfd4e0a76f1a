"""
Middleware untuk Whitelist Protection
"""

import logging
from typing import Callable, Dict, Any, Awaitable
from aiogram import BaseMiddleware
from aiogram.types import Message
from config import is_super_admin, is_whitelist_admin, CONTACT_ADMIN
from .whitelist_manager import is_user_whitelisted
from .user_history import record_user

class WhitelistMiddleware(BaseMiddleware):
    """
    Middleware untuk cek whitelist setiap pesan masuk
    """
    
    async def __call__(
        self,
        handler: Callable[[Message, Dict[str, Any]], Awaitable[Any]],
        event: Message,
        data: Dict[str, Any]
    ) -> Any:
        """
        Main middleware function
        """
        
        # Skip jika bukan private message
        if event.chat.type != "private":
            return await handler(event, data)
        
        # Skip jika tidak ada from_user (seharusnya tidak mungkin)
        if not event.from_user:
            return await handler(event, data)
        
        user_id = event.from_user.id
        username = event.from_user.username or ""
        first_name = event.from_user.first_name or ""
        last_name = event.from_user.last_name or ""

        # Auto-record user ke database (sebelum cek whitelist)
        try:
            await record_user(user_id, username, first_name, last_name)
        except Exception as e:
            logging.error(f"Error recording user {user_id}: {e}")

        # Admin selalu bisa akses (super admin dan whitelist admin)
        if is_super_admin(username) or is_whitelist_admin(username):
            return await handler(event, data)
        
        # Cek whitelist untuk user biasa
        try:
            if await is_user_whitelisted(user_id):
                return await handler(event, data)
        except Exception as e:
            logging.error(f"Error checking whitelist for user {user_id}: {e}")
            # Jika error, tetap lanjut ke handler (fail-safe)
            return await handler(event, data)
        
        # User tidak diizinkan - kirim pesan penolakan
        await self.send_access_denied_message(event)
        
        # Log attempt akses yang ditolak
        self.log_denied_access(event)
        
        # Stop processing (tidak lanjut ke handler)
        return

    async def send_access_denied_message(self, message: Message):
        """Kirim pesan penolakan akses"""
        try:
            denial_message = (
                "🚫 **Akses Ditolak**\n"
                f"📞 **Hubungi {CONTACT_ADMIN} untuk akses:**"
            )
            await message.answer(denial_message, parse_mode="Markdown")
        except Exception as e:
            logging.error(f"Error sending access denied message: {e}")

    def log_denied_access(self, message: Message):
        """Log attempt akses yang ditolak"""
        try:
            user_id = message.from_user.id
            username = message.from_user.username or "no_username"
            first_name = message.from_user.first_name or ""
            last_name = message.from_user.last_name or ""
            full_name = f"{first_name} {last_name}".strip() or "no_name"
            
            message_text = message.text[:50] if message.text else "non_text_message"
            
            logging.warning(
                f"Access denied - User: {user_id} (@{username}, {full_name}) "
                f"tried: '{message_text}'"
            )
        except Exception as e:
            logging.error(f"Error logging denied access: {e}")

# Instance middleware yang bisa digunakan
whitelist_middleware = WhitelistMiddleware()
