import re
import logging
from utils.phone_converter import convert_08_to_62

def clean_and_validate_number(line):
    """
    Bersihkan dan validasi nomor dengan konversi 08→628:
    - ✅ KONVERSI 08→628: Convert 08xxx ke 628xxx di awal nomor
    - <PERSON>pus semua karakter kecuali angka dan +
    - <PERSON><PERSON> ada + di awal, pertah<PERSON>kan, sisanya hanya angka
    - Jika tidak ada + di awal, tambahkan + di depan
    - Nomor valid: minimal 8 digit angka (tanpa +)
    - Return nomor valid dengan + di depan, atau None jika tidak valid
    """
    line = line.strip()
    if not line:
        return None

    # ✅ KONVERSI 08→628: Convert sebelum processing lainnya
    line = convert_08_to_62(line)

    # Hapus semua karakter kecuali angka dan +
    if line.startswith("+"):
        # Pertahankan + di depan, sisanya hanya angka
        nomor = "+" + re.sub(r"[^\d]", "", line[1:])
    else:
        nomor = re.sub(r"[^\d]", "", line)
        nomor = "+" + nomor
    # Validasi: minimal 8 digit angka (tanpa +)
    digit_count = len(re.sub(r"[^\d]", "", nomor))
    if digit_count < 8:
        return None
    return nomor

def extract_valid_numbers_from_lines(lines):
    """
    Dari list baris, ambil hanya nomor valid (sudah dibersihkan dan diverifikasi).
    Includes automatic 08→628 conversion.
    """
    numbers = []
    conversion_count = 0

    for line in lines:
        original_line = line.strip()
        nomor = clean_and_validate_number(line)
        if nomor:
            numbers.append(nomor)
            # Count conversions untuk logging
            if original_line.startswith('08') and not original_line.startswith('+08'):
                conversion_count += 1

    # Log konversi jika ada
    if conversion_count > 0:
        logging.info(f"number_cleaner: Converted {conversion_count} numbers from 08->628")

    return numbers