"""
Admin Commands untuk Whitelist Management
"""

import logging
from typing import <PERSON>ple
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from aiogram.filters import Command
from config import is_whitelist_admin, CONTACT_ADMIN
from .whitelist_manager import (
    add_user_to_whitelist,
    remove_user_from_whitelist,
    get_whitelist_count,
    resolve_user_from_text
)

router = Router()

class WhitelistStates(StatesGroup):
    waiting_add_users = State()
    waiting_remove_users = State()

def format_user_display(username: str, display_name: str) -> Tuple[str, str]:
    """
    Format user display untuk konsistensi
    Returns: (username_text, name_text)
    """
    # Format username
    if username and username.startswith("@"):
        username_text = username
    elif username:
        username_text = f"@{username}"
    else:
        username_text = ""

    # Format name
    if display_name and display_name != username_text and not display_name.startswith("User ID:"):
        name_text = f" ({display_name})"
    else:
        name_text = ""

    return username_text, name_text

async def send_access_denied_message(message: types.Message):
    """<PERSON><PERSON> pesan akses ditolak untuk non-admin"""
    denial_message = (
        "🚫 **Akses Ditolak**\n"
        f"📞 **Hubungi {CONTACT_ADMIN} untuk akses:**"
    )
    await message.answer(denial_message, parse_mode="Markdown")

@router.message(Command("tambah"), F.chat.type == "private")
async def tambah_user_start(message: types.Message, state: FSMContext):
    """Handler untuk command /tambah"""
    username = message.from_user.username or ""
    
    # Cek akses admin
    if not is_whitelist_admin(username):
        await send_access_denied_message(message)
        return
    
    # Clear state sebelumnya
    await state.clear()
    
    # Set state untuk menunggu input user
    await state.set_state(WhitelistStates.waiting_add_users)
    
    bot_msg = (
        "📝 **Tambah User ke Whitelist**\n\n"
        "Kirim username, nama, atau ID user yang ingin diizinkan.\n"
        "Bisa kirim beberapa sekaligus (pisah per baris).\n\n"
        "**Contoh:**\n"
        "@username1\n"
        "Nama Pengguna\n"
        "123456789\n\n"
        "💡 **Tips:** Forward pesan dari user untuk mendapatkan ID otomatis."
    )
    await message.answer(bot_msg, parse_mode="Markdown")

@router.message(WhitelistStates.waiting_add_users, F.chat.type == "private")
async def tambah_user_process(message: types.Message, state: FSMContext):
    """Process input user untuk ditambah ke whitelist"""

    # Cek jika user mengetik command lain
    if message.text and message.text.strip().startswith("/"):
        await state.clear()
        return

    # Cek jika ada forward message
    if message.forward_from:
        # Ambil info dari forward message
        user_id = message.forward_from.id
        username = message.forward_from.username or ""
        first_name = message.forward_from.first_name or ""
        last_name = message.forward_from.last_name or ""
        full_name = f"{first_name} {last_name}".strip()

        # Tambah ke whitelist
        success = await add_user_to_whitelist(user_id)

        formatted_username = f"@{username}" if username else ""
        username_text, name_text = format_user_display(formatted_username, full_name)

        if success:
            response = (
                f"✅ **Berhasil menambahkan user dari forward message:**\n\n"
                f"1️⃣ {username_text}{name_text}\n"
                f"   🆔 ID: {user_id}\n\n"
                f"📊 **Total user yang diizinkan: {await get_whitelist_count()} user**"
            )
        else:
            response = (
                f"🔄 **User sudah ada di whitelist:**\n\n"
                f"1️⃣ {username_text}{name_text}\n"
                f"   🆔 ID: {user_id}\n\n"
                f"📊 **Total user yang diizinkan: {await get_whitelist_count()} user**"
            )

        await message.answer(response, parse_mode="Markdown")
        await state.clear()
        return

    if not message.text:
        await message.answer("📝 Kirim username, nama, ID user, atau forward pesan dari user yang ingin ditambahkan.")
        return
    
    # Parse input (pisah per baris)
    lines = [line.strip() for line in message.text.strip().split('\n') if line.strip()]
    
    if not lines:
        await message.answer("📝 Kirim username, nama, atau ID user yang ingin ditambahkan.")
        return
    
    # Process setiap line
    added_users = []
    already_exists = []
    not_found = []
    
    for line in lines:
        try:
            user_info = await resolve_user_from_text(line)
            
            if user_info and user_info[0] > 0:  # Valid user ID
                user_id, username_found, display_name = user_info
                
                # Coba tambah ke whitelist
                success = await add_user_to_whitelist(user_id)
                
                if success:
                    added_users.append((user_id, username_found, display_name))
                else:
                    already_exists.append((user_id, username_found, display_name))
            else:
                not_found.append(line)
                
        except Exception as e:
            logging.error(f"Error processing user input '{line}': {e}")
            not_found.append(line)
    
    # Buat response message
    response_parts = []
    
    if added_users:
        response_parts.append(f"✅ **Berhasil menambahkan {len(added_users)} user ke whitelist:**\n")
        for i, (user_id, username, display_name) in enumerate(added_users, 1):
            username_text, name_text = format_user_display(username, display_name)
            response_parts.append(f"{i}️⃣ {username_text}{name_text}")
            response_parts.append(f"   🆔 ID: {user_id}\n")
    
    if already_exists:
        response_parts.append(f"🔄 **User sudah ada di whitelist ({len(already_exists)} user):**\n")
        for i, (user_id, username, display_name) in enumerate(already_exists, 1):
            username_text, name_text = format_user_display(username, display_name)
            response_parts.append(f"{i}️⃣ {username_text}{name_text}")
            response_parts.append(f"   🆔 ID: {user_id}\n")
    
    if not_found:
        response_parts.append(f"❌ **Tidak ditemukan ({len(not_found)} user):**\n")
        for i, user_input in enumerate(not_found, 1):
            response_parts.append(f"{i}️⃣ {user_input}\n")
        response_parts.append("\n💡 **Tips:** Untuk user yang tidak ditemukan, kirim User ID langsung (angka).")
    
    # Total count
    total_count = await get_whitelist_count()
    response_parts.append(f"📊 **Total user yang diizinkan: {total_count} user**")
    
    response_message = "\n".join(response_parts)
    await message.answer(response_message, parse_mode="Markdown")
    
    # Clear state
    await state.clear()

@router.message(Command("hapus"), F.chat.type == "private")
async def hapus_user_start(message: types.Message, state: FSMContext):
    """Handler untuk command /hapus"""
    username = message.from_user.username or ""
    
    # Cek akses admin
    if not is_whitelist_admin(username):
        await send_access_denied_message(message)
        return
    
    # Clear state sebelumnya
    await state.clear()
    
    # Set state untuk menunggu input user
    await state.set_state(WhitelistStates.waiting_remove_users)
    
    bot_msg = (
        "🗑️ **Hapus User dari Whitelist**\n\n"
        "Kirim username, nama, atau ID user yang ingin dihapus izinnya.\n"
        "Bisa kirim beberapa sekaligus (pisah per baris).\n\n"
        "**Contoh:**\n"
        "@username1\n"
        "Nama Pengguna\n"
        "123456789\n\n"
        "💡 **Tips:** Forward pesan dari user untuk mendapatkan ID otomatis."
    )
    await message.answer(bot_msg, parse_mode="Markdown")

@router.message(WhitelistStates.waiting_remove_users, F.chat.type == "private")
async def hapus_user_process(message: types.Message, state: FSMContext):
    """Process input user untuk dihapus dari whitelist"""

    # Cek jika user mengetik command lain
    if message.text and message.text.strip().startswith("/"):
        await state.clear()
        return

    # Cek jika ada forward message
    if message.forward_from:
        # Ambil info dari forward message
        user_id = message.forward_from.id
        username = message.forward_from.username or ""
        first_name = message.forward_from.first_name or ""
        last_name = message.forward_from.last_name or ""
        full_name = f"{first_name} {last_name}".strip()

        # Hapus dari whitelist
        success = await remove_user_from_whitelist(user_id)

        formatted_username = f"@{username}" if username else ""
        username_text, name_text = format_user_display(formatted_username, full_name)

        if success:
            response = (
                f"❌ **Berhasil menghapus user dari forward message:**\n\n"
                f"1️⃣ {username_text}{name_text}\n"
                f"   🆔 ID: {user_id}\n\n"
                f"📊 **Total user yang diizinkan: {await get_whitelist_count()} user**"
            )
        else:
            response = (
                f"⚠️ **User tidak ada di whitelist:**\n\n"
                f"1️⃣ {username_text}{name_text}\n"
                f"   🆔 ID: {user_id}\n\n"
                f"📊 **Total user yang diizinkan: {await get_whitelist_count()} user**"
            )

        await message.answer(response, parse_mode="Markdown")
        await state.clear()
        return

    if not message.text:
        await message.answer("🗑️ Kirim username, nama, ID user, atau forward pesan dari user yang ingin dihapus.")
        return
    
    # Parse input (pisah per baris)
    lines = [line.strip() for line in message.text.strip().split('\n') if line.strip()]
    
    if not lines:
        await message.answer("🗑️ Kirim username, nama, atau ID user yang ingin dihapus.")
        return
    
    # Process setiap line
    removed_users = []
    not_in_whitelist = []
    not_found = []
    
    for line in lines:
        try:
            user_info = await resolve_user_from_text(line)
            
            if user_info and user_info[0] > 0:  # Valid user ID
                user_id, username_found, display_name = user_info
                
                # Coba hapus dari whitelist
                success = await remove_user_from_whitelist(user_id)
                
                if success:
                    removed_users.append((user_id, username_found, display_name))
                else:
                    not_in_whitelist.append((user_id, username_found, display_name))
            else:
                not_found.append(line)
                
        except Exception as e:
            logging.error(f"Error processing user input '{line}': {e}")
            not_found.append(line)
    
    # Buat response message
    response_parts = []
    
    if removed_users:
        response_parts.append(f"❌ **Berhasil menghapus {len(removed_users)} user dari whitelist:**\n")
        for i, (user_id, username, display_name) in enumerate(removed_users, 1):
            username_text, name_text = format_user_display(username, display_name)
            response_parts.append(f"{i}️⃣ {username_text}{name_text}")
            response_parts.append(f"   🆔 ID: {user_id}\n")

    if not_in_whitelist:
        response_parts.append(f"⚠️ **User tidak ada di whitelist ({len(not_in_whitelist)} user):**\n")
        for i, (user_id, username, display_name) in enumerate(not_in_whitelist, 1):
            username_text, name_text = format_user_display(username, display_name)
            response_parts.append(f"{i}️⃣ {username_text}{name_text}")
            response_parts.append(f"   🆔 ID: {user_id}\n")
    
    if not_found:
        response_parts.append(f"❌ **Tidak ditemukan ({len(not_found)} user):**\n")
        for i, user_input in enumerate(not_found, 1):
            response_parts.append(f"{i}️⃣ {user_input}\n")
        response_parts.append("\n💡 **Tips:** Untuk user yang tidak ditemukan, kirim User ID langsung (angka).")
    
    # Total count
    total_count = await get_whitelist_count()
    response_parts.append(f"📊 **Total user yang diizinkan: {total_count} user**")
    
    response_message = "\n".join(response_parts)
    await message.answer(response_message, parse_mode="Markdown")
    
    # Clear state
    await state.clear()

# Handler untuk pesan lain saat waiting state
@router.message(WhitelistStates.waiting_add_users)
@router.message(WhitelistStates.waiting_remove_users)
async def handle_other_messages(message: types.Message, state: FSMContext):
    """Handler untuk pesan lain saat waiting state"""
    if message.text and message.text.startswith('/'):
        # Command lain, clear state
        await state.clear()
        return
    
    # Pesan biasa, abaikan atau beri instruksi
    current_state = await state.get_state()
    if current_state == WhitelistStates.waiting_add_users.state:
        await message.answer("📝 Kirim username, nama, atau ID user yang ingin ditambahkan.")
    elif current_state == WhitelistStates.waiting_remove_users.state:
        await message.answer("🗑️ Kirim username, nama, atau ID user yang ingin dihapus.")
