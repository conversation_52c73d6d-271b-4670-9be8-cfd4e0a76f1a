"""
Handler untuk fitur /getname - Extract nama file
"""

import logging
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from aiogram.filters import Command
# Tidak perlu logging untuk fitur ini

router = Router()

class GetNameStates(StatesGroup):
    waiting_files = State()

# Handler global untuk perintah lain agar bisa membatalkan proses ini
@router.message(Command("start"), F.chat.type == "private")
async def start_from_getname(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import start_handler
    await start_handler(message, state)

@router.message(Command("to_vcf"), F.chat.type == "private")
async def to_vcf_from_getname(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_vcf import to_vcf_start
    await to_vcf_start(message, state)

@router.message(Command("to_txt"), F.chat.type == "private")
async def to_txt_from_getname(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_txt import to_txt_start
    await to_txt_start(message, state)

@router.message(Command("admin"), F.chat.type == "private")
async def admin_from_getname(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.admin import admin_start
    await admin_start(message, state)

@router.message(Command("manual"), F.chat.type == "private")
async def manual_from_getname(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.manual import manual_start
    await manual_start(message, state)

@router.message(Command("add"), F.chat.type == "private")
async def add_from_getname(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.add import add_start
    await add_start(message, state)

@router.message(Command("delete"), F.chat.type == "private")
async def delete_from_getname(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.delete import delete_start
    await delete_start(message, state)

@router.message(Command("renamectc"), F.chat.type == "private")
async def renamectc_from_getname(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamectc import renamectc_start
    await renamectc_start(message, state)

@router.message(Command("renamefile"), F.chat.type == "private")
async def renamefile_from_getname(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamefile import renamefile_start
    await renamefile_start(message, state)

@router.message(Command("merge"), F.chat.type == "private")
async def merge_from_getname(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.merge import merge_start
    await merge_start(message, state)

@router.message(Command("split"), F.chat.type == "private")
async def split_from_getname(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.split import split_start
    await split_start(message, state)

@router.message(Command("count"), F.chat.type == "private")
async def count_from_getname(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.count import count_start
    await count_start(message, state)

@router.message(Command("nodup"), F.chat.type == "private")
async def nodup_from_getname(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.nodup import nodup_start
    await nodup_start(message, state)

@router.message(Command("generate"), F.chat.type == "private")
async def generate_from_getname(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.generate import generate_start
    await generate_start(message, state)

@router.message(Command("setting"), F.chat.type == "private")
async def setting_from_getname(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.hide_menu import hide_menu_start
    await hide_menu_start(message, state)

@router.message(Command("help"), F.chat.type == "private")
async def help_from_getname(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import help_handler
    await help_handler(message, state)

@router.message(Command("getname"), F.chat.type == "private")
async def getname_start(message: types.Message, state: FSMContext):
    """Handler untuk command /getname"""
    # Clear state sebelumnya
    await state.clear()
    
    # Set state untuk menunggu file
    await state.set_state(GetNameStates.waiting_files)
    
    # Initialize list untuk menyimpan nama file
    await state.update_data(filenames=[])
    
    # Kirim pesan minta file
    bot_msg = "📄 Kirim file untuk di ambil namanya"
    await message.answer(bot_msg)

@router.message(GetNameStates.waiting_files, F.document)
async def handle_document(message: types.Message, state: FSMContext):
    """Handler untuk file document"""
    if message.document and message.document.file_name:
        # Ambil nama file tanpa ekstensi
        filename = message.document.file_name
        name_only = filename.rsplit('.', 1)[0] if '.' in filename else filename

        # Simpan ke state
        data = await state.get_data()
        filenames = data.get('filenames', [])
        filenames.append(name_only)

        # Kirim konfirmasi hanya untuk file pertama
        if len(filenames) == 1:
            bot_msg = "✅ File diterima. Ketik /done jika sudah."
            await message.answer(bot_msg)

        await state.update_data(filenames=filenames)

@router.message(GetNameStates.waiting_files, F.photo)
async def handle_photo(message: types.Message, state: FSMContext):
    """Handler untuk file photo"""
    # Photo biasanya tidak punya nama file yang meaningful
    # Tapi kita tetap handle jika ada
    filename = "photo"
    if hasattr(message.photo[-1], 'file_name') and message.photo[-1].file_name:
        filename = message.photo[-1].file_name
        name_only = filename.rsplit('.', 1)[0] if '.' in filename else filename
    else:
        name_only = "photo"

    # Simpan ke state
    data = await state.get_data()
    filenames = data.get('filenames', [])
    filenames.append(name_only)

    # Kirim konfirmasi hanya untuk file pertama
    if len(filenames) == 1:
        bot_msg = "✅ File diterima. Ketik /done jika sudah."
        await message.answer(bot_msg)

    await state.update_data(filenames=filenames)

@router.message(GetNameStates.waiting_files, F.video)
async def handle_video(message: types.Message, state: FSMContext):
    """Handler untuk file video"""
    if message.video and message.video.file_name:
        filename = message.video.file_name
        name_only = filename.rsplit('.', 1)[0] if '.' in filename else filename
    else:
        name_only = "video"

    # Simpan ke state
    data = await state.get_data()
    filenames = data.get('filenames', [])
    filenames.append(name_only)

    # Kirim konfirmasi hanya untuk file pertama
    if len(filenames) == 1:
        bot_msg = "✅ File diterima. Ketik /done jika sudah."
        await message.answer(bot_msg)

    await state.update_data(filenames=filenames)

@router.message(GetNameStates.waiting_files, F.audio)
async def handle_audio(message: types.Message, state: FSMContext):
    """Handler untuk file audio"""
    if message.audio and message.audio.file_name:
        filename = message.audio.file_name
        name_only = filename.rsplit('.', 1)[0] if '.' in filename else filename
    elif message.audio and message.audio.title:
        name_only = message.audio.title
    else:
        name_only = "audio"

    # Simpan ke state
    data = await state.get_data()
    filenames = data.get('filenames', [])
    filenames.append(name_only)

    # Kirim konfirmasi hanya untuk file pertama
    if len(filenames) == 1:
        bot_msg = "✅ File diterima. Ketik /done jika sudah."
        await message.answer(bot_msg)

    await state.update_data(filenames=filenames)

@router.message(GetNameStates.waiting_files, F.voice)
async def handle_voice(message: types.Message, state: FSMContext):
    """Handler untuk voice message"""
    name_only = "voice_message"

    # Simpan ke state
    data = await state.get_data()
    filenames = data.get('filenames', [])
    filenames.append(name_only)

    # Kirim konfirmasi hanya untuk file pertama
    if len(filenames) == 1:
        bot_msg = "✅ File diterima. Ketik /done jika sudah."
        await message.answer(bot_msg)

    await state.update_data(filenames=filenames)

@router.message(GetNameStates.waiting_files, Command("done"))
async def getname_done(message: types.Message, state: FSMContext):
    """Handler untuk command /done"""
    # Ambil data dari state
    data = await state.get_data()
    filenames = data.get('filenames', [])
    
    if not filenames:
        bot_msg = "❌ Belum ada file yang dikirim"
        await message.answer(bot_msg)
        return
    
    # Format hasil dengan text yang bisa disalin (seperti ID)
    if len(filenames) == 1:
        # Single file dengan format copyable
        bot_msg = f"📝 Nama file:\n\n`{filenames[0]}`"
    else:
        # Multiple files dengan format copyable
        result_lines = ["📝 Nama file:\n"]
        for filename in filenames:
            result_lines.append(f"`{filename}`")
        bot_msg = "\n".join(result_lines)

    await message.answer(bot_msg, parse_mode="Markdown")
    
    # Clear state
    await state.clear()

@router.message(GetNameStates.waiting_files)
async def handle_other_messages(message: types.Message, state: FSMContext):
    """Handler untuk pesan lain saat waiting files"""
    if message.text and message.text.startswith('/'):
        # Command lain, clear state
        await state.clear()
        return
    
    # Pesan biasa, abaikan atau beri instruksi
    bot_msg = "📄 Kirim file untuk di ambil namanya"
    await message.answer(bot_msg)
