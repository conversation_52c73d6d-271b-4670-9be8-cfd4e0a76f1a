"""
Handler untuk fitur /generate - Generate nama file dengan numbering
"""

import re
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from aiogram.filters import Command

router = Router()

class GenerateStates(StatesGroup):
    waiting_filename = State()
    waiting_end_number = State()

# Handler global untuk perintah lain agar bisa membatalkan proses ini
@router.message(Command("start"), F.chat.type == "private")
async def start_from_generate(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import start_handler
    await start_handler(message, state)

@router.message(Command("to_vcf"), F.chat.type == "private")
async def to_vcf_from_generate(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_vcf import to_vcf_start
    await to_vcf_start(message, state)

@router.message(Command("to_txt"), F.chat.type == "private")
async def to_txt_from_generate(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_txt import to_txt_start
    await to_txt_start(message, state)

@router.message(Command("admin"), F.chat.type == "private")
async def admin_from_generate(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.admin import admin_start
    await admin_start(message, state)

@router.message(Command("manual"), F.chat.type == "private")
async def manual_from_generate(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.manual import manual_start
    await manual_start(message, state)

@router.message(Command("add"), F.chat.type == "private")
async def add_from_generate(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.add import add_start
    await add_start(message, state)

@router.message(Command("delete"), F.chat.type == "private")
async def delete_from_generate(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.delete import delete_start
    await delete_start(message, state)

@router.message(Command("renamectc"), F.chat.type == "private")
async def renamectc_from_generate(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamectc import renamectc_start
    await renamectc_start(message, state)

@router.message(Command("renamefile"), F.chat.type == "private")
async def renamefile_from_generate(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamefile import renamefile_start
    await renamefile_start(message, state)

@router.message(Command("merge"), F.chat.type == "private")
async def merge_from_generate(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.merge import merge_start
    await merge_start(message, state)

@router.message(Command("split"), F.chat.type == "private")
async def split_from_generate(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.split import split_start
    await split_start(message, state)

@router.message(Command("count"), F.chat.type == "private")
async def count_from_generate(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.count import count_start
    await count_start(message, state)

@router.message(Command("nodup"), F.chat.type == "private")
async def nodup_from_generate(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.nodup import nodup_start
    await nodup_start(message, state)

@router.message(Command("getname"), F.chat.type == "private")
async def getname_from_generate(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.getname import getname_start
    await getname_start(message, state)

@router.message(Command("setting"), F.chat.type == "private")
async def setting_from_generate(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.hide_menu import hide_menu_start
    await hide_menu_start(message, state)

@router.message(Command("help"), F.chat.type == "private")
async def help_from_generate(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import help_handler
    await help_handler(message, state)

@router.message(Command("generate"), F.chat.type == "private")
async def generate_start(message: types.Message, state: FSMContext):
    """Handler untuk command /generate"""
    # Clear state sebelumnya
    await state.clear()
    
    # Set state untuk menunggu nama file
    await state.set_state(GenerateStates.waiting_filename)
    
    # Kirim pesan minta nama file
    bot_msg = "📝 Tulisakan nama file nya"
    await message.answer(bot_msg)

@router.message(GenerateStates.waiting_filename)
async def handle_filename(message: types.Message, state: FSMContext):
    """Handler untuk nama file contoh"""
    if not message.text:
        await message.answer("📝 Tulisakan nama file nya")
        return
    
    # Cek jika user mengetik command lain
    if message.text.strip().startswith("/"):
        await state.clear()
        return
    
    filename = message.text.strip()
    
    # Detect angka terakhir di nama file
    numbers = re.findall(r'\d+', filename)
    
    if numbers:
        # Ada angka, ambil yang terakhir
        last_number = numbers[-1]
        start_number = int(last_number)
        
        # Detect format (leading zero atau tidak)
        if last_number.startswith('0') and len(last_number) > 1:
            # Ada leading zero
            number_format = len(last_number)  # 01 = 2 digit, 001 = 3 digit
        else:
            # Tidak ada leading zero
            number_format = 0
        
        # Cari posisi angka terakhir untuk replace nanti
        last_pos = filename.rfind(last_number)
        prefix = filename[:last_pos]
        suffix = filename[last_pos + len(last_number):]
        
    else:
        # Tidak ada angka, mulai dari 1
        start_number = 1
        number_format = 0
        prefix = filename + " "
        suffix = ""
    
    # Simpan data ke state
    await state.update_data(
        filename=filename,
        start_number=start_number,
        number_format=number_format,
        prefix=prefix,
        suffix=suffix
    )
    
    # Set state untuk menunggu angka akhir
    await state.set_state(GenerateStates.waiting_end_number)
    
    # Kirim pesan minta angka akhir
    bot_msg = "🔢 Sampai angka berapa?"
    await message.answer(bot_msg)

@router.message(GenerateStates.waiting_end_number)
async def handle_end_number(message: types.Message, state: FSMContext):
    """Handler untuk angka akhir"""
    if not message.text:
        await message.answer("🔢 Sampai angka berapa?")
        return
    
    # Cek jika user mengetik command lain
    if message.text.strip().startswith("/"):
        await state.clear()
        return
    
    try:
        end_number = int(message.text.strip())
    except ValueError:
        await message.answer("❌ Masukkan angka yang valid")
        return
    
    # Ambil data dari state
    data = await state.get_data()
    start_number = data.get('start_number')
    number_format = data.get('number_format')
    prefix = data.get('prefix')
    suffix = data.get('suffix')
    
    # Validasi range
    if end_number < start_number:
        await message.answer(f"❌ Angka akhir harus >= {start_number}")
        return
    
    # Generate sequence dengan format yang bisa "salin semua"
    generated_names = []

    for num in range(start_number, end_number + 1):
        if number_format > 0:
            # Dengan leading zero
            formatted_num = str(num).zfill(number_format)
        else:
            # Tanpa leading zero
            formatted_num = str(num)

        # Gabungkan prefix + number + suffix
        generated_name = f"{prefix}{formatted_num}{suffix}"
        generated_names.append(generated_name)

    # Format hasil dengan triple backticks - tanpa batasan
    bot_msg = (
        f"📝 **Hasil generate:**\n"
        f"```\n" +
        "\n".join(generated_names) +
        f"\n```"
    )

    await message.answer(bot_msg, parse_mode="Markdown")
    
    # Clear state
    await state.clear()

@router.message(GenerateStates.waiting_filename)
async def handle_other_messages_filename(message: types.Message, state: FSMContext):
    """Handler untuk pesan lain saat waiting filename"""
    if message.text and message.text.startswith('/'):
        # Command lain, clear state
        await state.clear()
        return
    
    # Pesan biasa, abaikan atau beri instruksi
    bot_msg = "📝 Tulisakan nama file nya"
    await message.answer(bot_msg)

@router.message(GenerateStates.waiting_end_number)
async def handle_other_messages_number(message: types.Message, state: FSMContext):
    """Handler untuk pesan lain saat waiting end number"""
    if message.text and message.text.startswith('/'):
        # Command lain, clear state
        await state.clear()
        return
    
    # Pesan biasa, abaikan atau beri instruksi
    bot_msg = "🔢 Sampai angka berapa?"
    await message.answer(bot_msg)
