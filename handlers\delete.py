import logging
import os
import time
import aiofiles
import asyncio
from aiogram import Router, types, F, Bot
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from aiogram.filters import Command
from utils.file import extract_numbers
from utils.number_cleaner import extract_valid_numbers_from_lines, clean_and_validate_number
from utils.retry_send import retry_send_document
from utils.user_isolation import with_user_isolation, is_user_busy
from utils.user_directories import get_user_file_path
from utils.smart_logging import log_user_smart, log_bot, flush_user_logs

router = Router()


class DeleteStates(StatesGroup):
    waiting_files = State()
    waiting_done = State()
    waiting_numbers = State()

# Gunakan smart logging dari utils
def log_user(message: types.Message):
    log_user_smart(message)

@router.message(Command("delete"), F.chat.type == "private")
async def delete_global(message: types.Message, state: FSMContext):
    await state.clear()
    await delete_start(message, state)

@router.message(Command("start"), F.chat.type == "private")
async def start_from_delete(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import start_handler
    await start_handler(message, state)

@router.message(Command("to_vcf"), F.chat.type == "private")
async def to_vcf_from_delete(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_vcf import to_vcf_start
    await to_vcf_start(message, state)

@router.message(Command("to_txt"), F.chat.type == "private")
async def to_txt_from_delete(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_txt import to_txt_start
    await to_txt_start(message, state)

@router.message(Command("admin"), F.chat.type == "private")
async def admin_from_delete(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.admin import admin_start
    await admin_start(message, state)

@router.message(Command("manual"), F.chat.type == "private")
async def manual_from_delete(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.manual import manual_start
    await manual_start(message, state)

@router.message(Command("add"), F.chat.type == "private")
async def add_from_delete(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.add import add_start
    await add_start(message, state)

@router.message(Command("renamectc"), F.chat.type == "private")
async def renamectc_from_delete(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamectc import renamectc_start
    await renamectc_start(message, state)

@router.message(Command("renamefile"), F.chat.type == "private")
async def renamefile_from_delete(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamefile import renamefile_start
    await renamefile_start(message, state)

@router.message(Command("merge"), F.chat.type == "private")
async def merge_from_delete(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.merge import merge_start
    await merge_start(message, state)

@router.message(Command("split"), F.chat.type == "private")
async def split_from_delete(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.split import split_start
    await split_start(message, state)

@router.message(Command("count"), F.chat.type == "private")
async def count_from_delete(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.count import count_start
    await count_start(message, state)

@router.message(Command("nodup"), F.chat.type == "private")
async def nodup_from_delete(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.nodup import nodup_start
    await nodup_start(message, state)

@router.message(Command("getname"), F.chat.type == "private")
async def getname_from_delete(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.getname import getname_start
    await getname_start(message, state)

@router.message(Command("generate"), F.chat.type == "private")
async def generate_from_delete(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.generate import generate_start
    await generate_start(message, state)

@router.message(Command("setting"), F.chat.type == "private")
async def setting_from_delete(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.hide_menu import hide_menu_start
    await hide_menu_start(message, state)

@router.message(Command("help"), F.chat.type == "private")
async def help_from_delete(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import help_handler
    await help_handler(message, state)

async def delete_start(message: types.Message, state: FSMContext):
    # Silent force release semaphore jika user stuck
    if is_user_busy(message.from_user.id):
        from utils.user_isolation import force_release_user_lock
        force_release_user_lock(message.from_user.id)
        await state.clear()
        # Lanjut ke processing normal tanpa pesan pembatalan

    log_user(message)

    # Flush pending file upload logs sebelum proses
    flush_user_logs(message.from_user.id)


    # Flush any pending logs dari command sebelumnya
    flush_user_logs(message.from_user.id)
    bot_msg = "🗑️ Kirim file yang ingin dihapus nomornya"
    await message.answer(bot_msg)
    log_bot(bot_msg)
    await state.set_state(DeleteStates.waiting_files)
    await state.update_data(files=[], logs=[], file_error=False)

@router.message(DeleteStates.waiting_files, F.document, F.chat.type == "private")
async def delete_receive_file(message: types.Message, state: FSMContext, bot: Bot):
    log_user(message)
    file = message.document
    _, ext = os.path.splitext(file.file_name.lower())
    allowed_ext = [".txt", ".xlsx", ".xls", ".vcf", ".csv"]

    data = await state.get_data()
    if data.get("file_error"):
        return

    if ext not in allowed_ext:
        await state.update_data(files=[], logs=[], file_error=True)
        bot_msg = "❌ Format file tidak didukung!\nKetik /delete untuk mulai ulang."
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return

    try:
        files = data.get("files", [])
        logs = data.get("logs", [])
        filename, ext_real = os.path.splitext(file.file_name)
        timestamp = int(time.time() * 1000)
        unique_name = f"{filename}_{timestamp}{ext_real}"
        file_path = get_user_file_path(message.from_user.id, unique_name)
        await bot.download(file, destination=file_path)
        files.append((file_path, file.file_name, message.message_id))
        logs.append((message.message_id, f"bot: File {file.file_name} diterima"))
        await state.update_data(files=files, logs=logs)
        state_now = await state.get_data()
        if len(state_now.get("files", [])) == 1 and not state_now.get("file_error"):
            bot_msg = "✅ File diterima. Ketik /done untuk lanjut."
            await message.answer(bot_msg)
            log_bot(bot_msg)
    except Exception as e:
        err_msg = "⚠️ Gagal menerima file. Coba lagi."
        log_bot(err_msg)
        logging.error(f"user: kirim file {file.file_name if 'file' in locals() else '[unknown]'} error: {e}")
        await message.answer(err_msg)

@router.message(DeleteStates.waiting_files, Command("done"), F.chat.type == "private")
async def delete_done(message: types.Message, state: FSMContext):
    log_user(message)
    data = await state.get_data()
    files = data.get("files", [])
    logs = data.get("logs", [])
    if not files:
        bot_msg = "⚠️ Belum ada file. Kirim file dulu."
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return
    files = sorted(files, key=lambda x: x[2])
    logs = sorted(logs, key=lambda x: x[0])
    await state.update_data(files=files, logs=logs)
    # Summary log untuk file reception
    total_files = len(files)
    if total_files > 0:
        logging.info(f"bot: {total_files} file diterima untuk delete")
    bot_msg = "🚫 Masukkan nomor yang ingin dihapus:"
    await message.answer(bot_msg)
    log_bot(bot_msg)
    await state.set_state(DeleteStates.waiting_numbers)

@router.message(DeleteStates.waiting_numbers, F.chat.type == "private")
async def delete_receive_numbers(message: types.Message, state: FSMContext):
    if message.text.strip().startswith("/"):
        await state.clear()
        return

    log_user(message)
    lines = message.text.strip().splitlines()
    numbers_to_delete = extract_valid_numbers_from_lines(lines)
    numbers_to_delete = list(dict.fromkeys(numbers_to_delete))
    if not numbers_to_delete:
        bot_msg = "Nomor tidak valid. Masukkan ulang nomor yang ingin dihapus (pisahkan per baris):"
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return
    await state.update_data(numbers_to_delete=numbers_to_delete)
    await process_delete_with_isolation(message, state)

async def process_delete_with_isolation(message, state):
    """
    Wrapper function untuk process_delete dengan user isolation.
    """
    from aiogram.exceptions import TelegramRetryAfter
    user_id = message.from_user.id

    # Jalankan process_delete dengan user isolation
    success, result = await with_user_isolation(user_id, process_delete, message, state)

    if not success:
        # User sedang busy
        bot_msg = f"⏳ {result}"
        try:
            await message.answer(bot_msg)
            log_bot(bot_msg)
        except TelegramRetryAfter:
            log_bot(bot_msg)

async def process_delete(message: types.Message, state: FSMContext):
    data = await state.get_data()
    files = data.get("files", [])
    numbers_to_delete = set(data.get("numbers_to_delete", []))
    file_paths_to_delete = []

    # Track apakah ada nomor yang ditemukan di file manapun
    found_any_number = False
    should_send_files = False

    try:
        for file_path, original_filename, _ in files:
            logging.info(f"user: proses file {os.path.basename(file_path)}")
            _, ext = os.path.splitext(original_filename.lower())

            # Track apakah nomor ditemukan di file ini
            found_in_this_file = False

            if ext == ".vcf":
                # Hapus hanya baris TEL yang nomornya cocok, struktur lain tetap
                async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
                    lines = await f.readlines()
                new_lines = []
                for line in lines:
                    if line.strip().startswith("TEL"):
                        nomor = line.split(":")[-1].strip()
                        nomor_bersih = clean_and_validate_number(nomor)
                        if nomor_bersih and nomor_bersih in numbers_to_delete:
                            found_in_this_file = True
                            found_any_number = True
                            continue  # skip baris ini
                    new_lines.append(line)
                output_path = get_user_file_path(message.from_user.id, original_filename)
                async with aiofiles.open(output_path, "w", encoding="utf-8") as f:
                    await f.writelines(new_lines)
            elif ext in [".txt", ".csv"]:
                old_numbers = await extract_numbers(file_path)
                # Cek apakah ada nomor yang akan dihapus
                for num in old_numbers:
                    if num in numbers_to_delete:
                        found_in_this_file = True
                        found_any_number = True
                        break
                new_numbers = [n for n in old_numbers if n not in numbers_to_delete]
                async with aiofiles.open(get_user_file_path(message.from_user.id, original_filename), "w", encoding="utf-8") as f:
                    await f.write("\n".join(new_numbers))
            elif ext in [".xlsx", ".xls"]:
                import pandas as pd
                old_numbers = await extract_numbers(file_path)
                # Cek apakah ada nomor yang akan dihapus
                for num in old_numbers:
                    if num in numbers_to_delete:
                        found_in_this_file = True
                        found_any_number = True
                        break
                new_numbers = [n for n in old_numbers if n not in numbers_to_delete]
                df = pd.DataFrame({"Nomor": new_numbers})
                df.to_excel(get_user_file_path(message.from_user.id, original_filename), index=False)
            else:
                old_numbers = await extract_numbers(file_path)
                # Cek apakah ada nomor yang akan dihapus
                for num in old_numbers:
                    if num in numbers_to_delete:
                        found_in_this_file = True
                        found_any_number = True
                        break
                new_numbers = [n for n in old_numbers if n not in numbers_to_delete]
                from utils.format import write_text_file
                await write_text_file(get_user_file_path(message.from_user.id, original_filename), "\n".join(new_numbers))

            # Selalu buat file output untuk semua file (baik yang berubah maupun tidak)
            file_paths_to_delete.append(get_user_file_path(message.from_user.id, original_filename))

        # Cek apakah nomor ditemukan di file manapun
        if not found_any_number:
            # Jika tidak ada nomor yang ditemukan, kirim pesan error dan jangan kirim file
            bot_msg = "❌ Nomor tidak ditemukan di file manapun"
            await message.answer(bot_msg)
            log_bot(bot_msg)
            should_send_files = False
        else:
            # Jika ada nomor yang ditemukan, kirim semua file
            for file_path, original_filename, _ in files:
                await retry_send_document(message, get_user_file_path(message.from_user.id, original_filename), original_filename)
                # log_bot(f"kirim file {original_filename}")  # Dikurangi untuk mengurangi spam log
            bot_msg = "📤 File berhasil dikirim!"
            await message.answer(bot_msg)
            log_bot(bot_msg)
            should_send_files = True
    except Exception as e:
        err_msg = f"❌ Gagal proses file. Ketik /delete untuk ulang.\n{e}"
        logging.error(err_msg)
        log_bot(err_msg)
        await message.answer(err_msg)
    finally:
        # Hanya hapus file hasil jika file sudah dikirim
        if should_send_files:
            async def remove_file(path):
                try:
                    if os.path.exists(path):
                        os.remove(path)
                        # logging.info(f"File hasil dihapus: {path}")
                except Exception as e:
                    logging.error(f"Gagal hapus file hasil: {path} ({e})")
            await asyncio.gather(*(remove_file(path) for path in file_paths_to_delete))
        await state.clear()