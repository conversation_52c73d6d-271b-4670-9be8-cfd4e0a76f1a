"""
User History Manager - Manage database user.txt
Format: @username|full_name|user_id
"""

import os
import logging
import aiofiles
from typing import List, Optional, Tuple
from datetime import datetime

USER_HISTORY_FILE = "access_control/database/user.txt"

async def ensure_user_history_file():
    """Pastikan file user history ada"""
    os.makedirs(os.path.dirname(USER_HISTORY_FILE), exist_ok=True)
    if not os.path.exists(USER_HISTORY_FILE):
        async with aiofiles.open(USER_HISTORY_FILE, "w", encoding="utf-8") as f:
            await f.write("# User History Database\n")
            await f.write("# Format: @username|full_name|user_id\n")
            await f.write("# Auto-generated, jangan edit manual\n\n")

async def load_user_history() -> List[Tuple[str, str, int]]:
    """
    Load user history dari file
    Returns: List of (username, full_name, user_id)
    """
    await ensure_user_history_file()
    users = []
    
    try:
        async with aiofiles.open(USER_HISTORY_FILE, "r", encoding="utf-8") as f:
            lines = await f.readlines()
            
        for line in lines:
            line = line.strip()
            if line and not line.startswith("#"):
                parts = line.split("|")
                if len(parts) == 3:
                    username, full_name, user_id_str = parts
                    try:
                        user_id = int(user_id_str)
                        users.append((username, full_name, user_id))
                    except ValueError:
                        logging.warning(f"Invalid user ID in line: {line}")
                        
    except Exception as e:
        logging.error(f"Error loading user history: {e}")
        
    return users

async def save_user_history(users: List[Tuple[str, str, int]]):
    """Save user history ke file"""
    await ensure_user_history_file()
    
    try:
        async with aiofiles.open(USER_HISTORY_FILE, "w", encoding="utf-8") as f:
            await f.write("# User History Database\n")
            await f.write("# Format: @username|full_name|user_id\n")
            await f.write("# Auto-generated, jangan edit manual\n\n")
            
            # Sort by user_id untuk konsistensi
            sorted_users = sorted(users, key=lambda x: x[2])
            
            for username, full_name, user_id in sorted_users:
                await f.write(f"{username}|{full_name}|{user_id}\n")
                
    except Exception as e:
        logging.error(f"Error saving user history: {e}")
        raise

async def record_user(user_id: int, username: str = "", first_name: str = "", last_name: str = ""):
    """
    Record atau update user di database
    """
    try:
        # Format username dengan @
        formatted_username = f"@{username}" if username else ""
        
        # Format full name
        full_name = f"{first_name} {last_name}".strip()
        if not full_name:
            full_name = "Unknown User"
        
        # Load existing users
        users = await load_user_history()
        
        # Cek apakah user sudah ada
        user_exists = False
        updated_users = []
        
        for existing_username, existing_full_name, existing_user_id in users:
            if existing_user_id == user_id:
                # Update existing user
                updated_users.append((formatted_username, full_name, user_id))
                user_exists = True
            else:
                # Keep existing user
                updated_users.append((existing_username, existing_full_name, existing_user_id))
        
        # Jika user belum ada, tambahkan
        if not user_exists:
            updated_users.append((formatted_username, full_name, user_id))
        
        # Save updated users
        await save_user_history(updated_users)
        
        logging.info(f"User recorded: {formatted_username} ({full_name}) - ID: {user_id}")
        
    except Exception as e:
        logging.error(f"Error recording user {user_id}: {e}")

async def find_user_by_username(search_username: str) -> Optional[Tuple[str, str, int]]:
    """
    Cari user berdasarkan username
    Returns: (username, full_name, user_id) atau None
    """
    try:
        # Pastikan search_username pakai @
        if not search_username.startswith("@"):
            search_username = f"@{search_username}"
        
        users = await load_user_history()
        
        for username, full_name, user_id in users:
            if username.lower() == search_username.lower():
                return (username, full_name, user_id)
                
    except Exception as e:
        logging.error(f"Error finding user by username {search_username}: {e}")
        
    return None

async def find_user_by_name(search_name: str) -> Optional[Tuple[str, str, int]]:
    """
    Cari user berdasarkan nama (partial match, case insensitive)
    Returns: (username, full_name, user_id) atau None
    """
    try:
        search_name_lower = search_name.lower()
        users = await load_user_history()
        
        # Exact match dulu
        for username, full_name, user_id in users:
            if full_name.lower() == search_name_lower:
                return (username, full_name, user_id)
        
        # Partial match
        for username, full_name, user_id in users:
            if search_name_lower in full_name.lower():
                return (username, full_name, user_id)
                
    except Exception as e:
        logging.error(f"Error finding user by name {search_name}: {e}")
        
    return None

async def find_user_by_id(user_id: int) -> Optional[Tuple[str, str, int]]:
    """
    Cari user berdasarkan user ID
    Returns: (username, full_name, user_id) atau None
    """
    try:
        users = await load_user_history()
        
        for username, full_name, existing_user_id in users:
            if existing_user_id == user_id:
                return (username, full_name, user_id)
                
    except Exception as e:
        logging.error(f"Error finding user by ID {user_id}: {e}")
        
    return None

async def get_user_count() -> int:
    """Get jumlah user di database"""
    try:
        users = await load_user_history()
        return len(users)
    except Exception as e:
        logging.error(f"Error getting user count: {e}")
        return 0

async def search_users(query: str) -> List[Tuple[str, str, int]]:
    """
    Search users berdasarkan query (username atau nama)
    Returns: List of matching users
    """
    try:
        query_lower = query.lower()
        users = await load_user_history()
        matches = []
        
        for username, full_name, user_id in users:
            # Cek username (tanpa @)
            username_clean = username[1:] if username.startswith("@") else username
            if query_lower in username_clean.lower():
                matches.append((username, full_name, user_id))
                continue
                
            # Cek full name
            if query_lower in full_name.lower():
                matches.append((username, full_name, user_id))
                continue
        
        return matches
        
    except Exception as e:
        logging.error(f"Error searching users with query '{query}': {e}")
        return []
