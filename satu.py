import logging
import os
import sys
from aiogram import <PERSON><PERSON>, Dispatcher
from aiogram.fsm.storage.memory import MemoryStorage
from config import BOT_TOKEN
from handlers import (
    to_vcf, done, start, to_txt, admin, manual, add, delete,
    renamectc, renamefile, merge, split, count, nodup, hide_menu,
    generate, getname, cancel,
)
from management import clear_data
from access_control.middleware import whitelist_middleware
from access_control import admin_commands

# Setup logging tanpa tanggal/waktu, pastikan log info tampil di terminal
logging.basicConfig(
    level=logging.INFO,
    format="%(levelname)s %(message)s",
    stream=sys.stdout
)

# Sembunyikan log internal aiogram "Update id=... is handled..." dari terminal
logging.getLogger("aiogram.event").setLevel(logging.WARNING)
logging.getLogger("aiogram.dispatcher").setLevel(logging.WARNING)
logging.getLogger("aiogram.dispatcher.dispatcher").setLevel(logging.WARNING)

# INFO: Logging dari handler u<PERSON><PERSON> (folder handlers) tetap aktif
# Background tasks, anti-link, clean system message, dan fitur admin sudah di-disable untuk mengurangi noise

# Pastikan folder data, management, dan access_control ada
os.makedirs("data", exist_ok=True)
os.makedirs("management", exist_ok=True)
os.makedirs("access_control/database", exist_ok=True)

# Global bot instance untuk digunakan di module lain
bot = None

async def main():
    global bot
    logging.info("Bot is starting...")
    bot = Bot(token=BOT_TOKEN)
    dp = Dispatcher(storage=MemoryStorage())

    # Register whitelist middleware (harus sebelum routers)
    dp.message.middleware(whitelist_middleware)

    # Register routers
    dp.include_router(admin_commands.router)  # Admin commands untuk whitelist
    dp.include_router(cancel.router)
    dp.include_router(start.router)
    dp.include_router(generate.router)
    dp.include_router(getname.router)
    dp.include_router(to_vcf.router)
    dp.include_router(hide_menu.router)
    dp.include_router(to_txt.router)
    dp.include_router(add.router)
    dp.include_router(delete.router)
    dp.include_router(renamectc.router)
    dp.include_router(renamefile.router)
    dp.include_router(merge.router)
    dp.include_router(split.router)
    dp.include_router(count.router)
    dp.include_router(nodup.router)
    dp.include_router(done.router)
    dp.include_router(clear_data.router)
    dp.include_router(admin.router)
    dp.include_router(manual.router)

    # Start background tasks untuk optimisasi
    try:
        from management.background_tasks import start_background_tasks
        await start_background_tasks(bot)
        # logging.info("Background tasks started successfully")  # Disabled untuk mengurangi noise
    except Exception as e:
        logging.warning(f"Failed to start background tasks: {e}")

    try:
        await dp.start_polling(bot)
    finally:
        # Stop background tasks saat bot berhenti
        try:
            from management.background_tasks import stop_background_tasks
            await stop_background_tasks()
            logging.info("Background tasks stopped")
        except Exception as e:
            logging.warning(f"Error stopping background tasks: {e}")



    logging.info("Bot stopped.")

if __name__ == "__main__":
    import asyncio
    try:
        asyncio.run(main())
    except (KeyboardInterrupt, SystemExit):
        logging.info("Bot terminated by user.")