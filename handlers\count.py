import logging
import os
import time
import aiofiles
import asyncio
from aiogram import Router, types, F, Bot
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from aiogram.filters import Command
from utils.retry_send import retry_send_document
from utils.user_isolation import with_user_isolation, is_user_busy
from utils.user_directories import get_user_file_path
from utils.smart_logging import log_user_smart, log_bot, flush_user_logs

router = Router()

class CountStates(StatesGroup):
    waiting_files = State()
    waiting_done = State()

# Gunakan smart logging dari utils
def log_user(message: types.Message):
    log_user_smart(message)

def clean_and_validate_number(line):
    import re
    line = line.strip()
    if not line:
        return None
    if line.startswith("+"):
        nomor = "+" + re.sub(r"[^\d]", "", line[1:])
    else:
        nomor = re.sub(r"[^\d]", "", line)
        nomor = "+" + nomor
    digit_count = len(re.sub(r"[^\d]", "", nomor))
    if digit_count < 8:
        return None
    return nomor

async def extract_numbers_from_file(file_path, ext):
    numbers = []
    if ext == ".vcf":
        async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
            content = await f.read()
        cards = content.split("BEGIN:VCARD")
        for card in cards:
            card = card.strip()
            if not card:
                continue
            for line in card.splitlines():
                if line.strip().startswith("TEL"):
                    nomor = line.split(":")[-1].strip()
                    nomor_bersih = clean_and_validate_number(nomor)
                    if nomor_bersih:
                        numbers.append(nomor_bersih)
    elif ext in [".txt", ".csv"]:
        async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
            lines = await f.readlines()
        for line in lines:
            nomor = clean_and_validate_number(line)
            if nomor:
                numbers.append(nomor)
    elif ext in [".xlsx", ".xls"]:
        import pandas as pd
        df = pd.read_excel(file_path)
        for col in df.columns:
            for val in df[col]:
                nomor = clean_and_validate_number(str(val))
                if nomor:
                    numbers.append(nomor)
    else:
        # Format lain: treat as txt
        async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
            lines = await f.readlines()
        for line in lines:
            nomor = clean_and_validate_number(line)
            if nomor:
                numbers.append(nomor)
    return numbers

# Handler global untuk perintah lain agar bisa membatalkan proses ini
@router.message(Command("start"), F.chat.type == "private")
async def start_from_count(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import start_handler
    await start_handler(message, state)

@router.message(Command("to_vcf"), F.chat.type == "private")
async def to_vcf_from_count(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_vcf import to_vcf_start
    await to_vcf_start(message, state)

@router.message(Command("to_txt"), F.chat.type == "private")
async def to_txt_from_count(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_txt import to_txt_start
    await to_txt_start(message, state)

@router.message(Command("admin"), F.chat.type == "private")
async def admin_from_count(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.admin import admin_start
    await admin_start(message, state)

@router.message(Command("manual"), F.chat.type == "private")
async def manual_from_count(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.manual import manual_start
    await manual_start(message, state)

@router.message(Command("add"), F.chat.type == "private")
async def add_from_count(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.add import add_start
    await add_start(message, state)

@router.message(Command("delete"), F.chat.type == "private")
async def delete_from_count(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.delete import delete_start
    await delete_start(message, state)

@router.message(Command("renamectc"), F.chat.type == "private")
async def renamectc_from_count(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamectc import renamectc_start
    await renamectc_start(message, state)

@router.message(Command("renamefile"), F.chat.type == "private")
async def renamefile_from_count(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamefile import renamefile_start
    await renamefile_start(message, state)

@router.message(Command("merge"), F.chat.type == "private")
async def merge_from_count(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.merge import merge_start
    await merge_start(message, state)

@router.message(Command("split"), F.chat.type == "private")
async def split_from_count(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.split import split_start
    await split_start(message, state)

@router.message(Command("nodup"), F.chat.type == "private")
async def nodup_from_count(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.nodup import nodup_start
    await nodup_start(message, state)

@router.message(Command("getname"), F.chat.type == "private")
async def getname_from_count(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.getname import getname_start
    await getname_start(message, state)

@router.message(Command("generate"), F.chat.type == "private")
async def generate_from_count(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.generate import generate_start
    await generate_start(message, state)

@router.message(Command("setting"), F.chat.type == "private")
async def setting_from_count(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.hide_menu import hide_menu_start
    await hide_menu_start(message, state)

@router.message(Command("help"), F.chat.type == "private")
async def help_from_count(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import help_handler
    await help_handler(message, state)

@router.message(Command("count"), F.chat.type == "private")
async def count_start(message: types.Message, state: FSMContext):
    await state.clear()

    # Silent force release semaphore jika user stuck
    if is_user_busy(message.from_user.id):
        from utils.user_isolation import force_release_user_lock
        force_release_user_lock(message.from_user.id)
        await state.clear()
        # Lanjut ke processing normal tanpa pesan pembatalan

    log_user(message)

    # Flush any pending logs dari command sebelumnya
    flush_user_logs(message.from_user.id)

    bot_msg = "📎 Kirim file yang mau dihitung kontaknya."
    await message.answer(bot_msg)
    log_bot(bot_msg)
    await state.set_state(CountStates.waiting_files)
    await state.update_data(files=[], logs=[], file_error=False)

@router.message(CountStates.waiting_files, F.document, F.chat.type == "private")
async def count_receive_file(message: types.Message, state: FSMContext, bot: Bot):
    log_user(message)
    file = message.document
    _, ext = os.path.splitext(file.file_name.lower())
    allowed_ext = [".txt", ".xlsx", ".xls", ".vcf", ".csv"]
    data = await state.get_data()
    if data.get("file_error"):
        return
    if ext not in allowed_ext:
        await state.update_data(files=[], logs=[], file_error=True)
        bot_msg = "❌ Format file tidak didukung!\nUlangi dengan /count"
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return
    try:
        files = data.get("files", [])
        logs = data.get("logs", [])
        filename, ext_real = os.path.splitext(file.file_name)
        timestamp = int(time.time() * 1000)
        unique_name = f"{filename}_{timestamp}{ext_real}"
        file_path = get_user_file_path(message.from_user.id, unique_name)
        await bot.download(file, destination=file_path)
        files.append((file_path, file.file_name, message.message_id))
        logs.append((message.message_id, f"bot: File {file.file_name} diterima"))
        await state.update_data(files=files, logs=logs)
        state_now = await state.get_data()
        if len(state_now.get("files", [])) == 1 and not state_now.get("file_error"):
            bot_msg = "✅ File diterima. Ketik /done jika sudah."
            await message.answer(bot_msg)
            log_bot(bot_msg)
    except Exception as e:
        err_msg = "⚠️ Gagal menerima file. Coba lagi."
        log_bot(err_msg)
        logging.error(f"user: kirim file {file.file_name if 'file' in locals() else '[unknown]'} error: {e}")
        await message.answer(err_msg)

@router.message(CountStates.waiting_files, Command("done"), F.chat.type == "private")
async def count_done(message: types.Message, state: FSMContext):
    log_user(message)

    # Flush pending file upload logs sebelum proses
    flush_user_logs(message.from_user.id)

    data = await state.get_data()
    files = data.get("files", [])
    logs = data.get("logs", [])
    if not files:
        bot_msg = "⚠️ Belum ada file. Kirim file dulu."
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return
    files = sorted(files, key=lambda x: x[2])
    logs = sorted(logs, key=lambda x: x[0])
    await state.update_data(files=files, logs=logs)

    # Summary log untuk file reception
    total_files = len(files)
    if total_files > 0:
        logging.info(f"bot: {total_files} file diterima untuk count")

    await process_count_with_isolation(message, state, files)

async def process_count_with_isolation(message, state, files):
    """
    Wrapper function untuk process_count dengan user isolation.
    """
    from aiogram.exceptions import TelegramRetryAfter
    user_id = message.from_user.id

    # Jalankan process_count dengan user isolation
    success, result = await with_user_isolation(user_id, process_count, message, state, files)

    if not success:
        # User sedang busy
        bot_msg = f"⏳ {result}"
        try:
            await message.answer(bot_msg)
            log_bot(bot_msg)
        except TelegramRetryAfter:
            log_bot(bot_msg)

async def process_count(message, state, files):
    """
    Fungsi utama untuk memproses count.
    """
    total_all = 0
    msg_lines = []
    try:
        for file_path, original_filename, _ in files:
            _, ext = os.path.splitext(original_filename.lower())
            numbers = await extract_numbers_from_file(file_path, ext)
            jumlah = len(numbers)
            total_all += jumlah
            msg_lines.append(f"{original_filename}: {jumlah} kontak")
        msg_lines.append(f"Total semua file: {total_all} kontak")
        bot_msg = "📊 Hasil hitung kontak:\n" + "\n".join(msg_lines)
        await message.answer(bot_msg)
        log_bot(bot_msg)
    except Exception as e:
        err_msg = f"❌ Gagal hitung kontak. Ulangi dengan /count\n{e}"
        logging.error(err_msg)
        log_bot(err_msg)
        await message.answer(err_msg)
    finally:
        # Hapus file upload setelah proses
        async def remove_file(path):
            try:
                if os.path.exists(path):
                    os.remove(path)
                    # logging.info(f"File upload dihapus: {path}")
            except Exception as e:
                logging.error(f"Gagal hapus file upload: {path} ({e})")
        await asyncio.gather(*(remove_file(f[0]) for f in files))
        await state.clear()