import asyncio
import logging
from aiogram.types import FSInputFile, InputMediaDocument
from aiogram.exceptions import TelegramNetworkError, TelegramRetryAfter

async def retry_send_document(message, file_path, filename, max_retry=5, delay=2, user_id=None):
    """
    Kirim dokumen ke Telegram dengan retry jika timeout/network error.
    Tidak ada pesan ke user saat retry, hanya jika sudah gagal 5x.
    Mendukung cancel mechanism.
    """
    # Check cancel flag sebelum mulai
    if user_id:
        from .user_isolation import is_user_cancelled
        if is_user_cancelled(user_id):
            return False  # Cancelled, jangan kirim

    for attempt in range(1, max_retry + 1):
        # Check cancel flag di setiap retry
        if user_id:
            from .user_isolation import is_user_cancelled
            if is_user_cancelled(user_id):
                return False  # Cancelled during retry

        try:
            await message.answer_document(FSInputFile(file_path, filename=filename))
            return True
        except TelegramRetryAfter as e:
            # Rate limiting - tunggu sesuai yang diminta Telegram
            wait_time = e.retry_after + 1  # Tambah 1 detik untuk safety
            logging.warning(f"Rate limit hit, waiting {wait_time} seconds")
            await asyncio.sleep(wait_time)
            # Jangan hitung sebagai attempt yang gagal untuk rate limit
            continue
        except (asyncio.TimeoutError, TelegramNetworkError):
            if attempt == max_retry:
                # Tidak kirim error message di sini - akan dihandle di send_files_with_sending_mode
                return False
            await asyncio.sleep(delay)
        except Exception as e:
            logging.error(f"Unexpected error sending document: {e}")
            # Tidak kirim error message di sini - akan dihandle di send_files_with_sending_mode
            return False
    return False

async def retry_send_documents_group(message, files_data, batch_size=10, max_retry=5, delay=2, user_id=None):
    """
    Kirim multiple dokumen ke Telegram sebagai media group dengan retry.
    Mendukung cancel mechanism.

    Args:
        message: Message object dari aiogram
        files_data: List of tuples (file_path, filename)
        batch_size: Maksimal file per group (default 10, sesuai limit Telegram)
        max_retry: Maksimal retry per batch
        delay: Delay antar retry
        user_id: ID user untuk cancel checking

    Returns:
        bool: True jika semua berhasil, False jika ada yang gagal
    """
    if not files_data:
        return True

    # Check cancel flag sebelum mulai
    if user_id:
        from .user_isolation import is_user_cancelled
        if is_user_cancelled(user_id):
            return False  # Cancelled, jangan kirim

    # Jika hanya 1 file, gunakan fungsi individual
    if len(files_data) == 1:
        file_path, filename = files_data[0]
        return await retry_send_document(message, file_path, filename, max_retry, delay, user_id)

    success_count = 0
    total_files = len(files_data)

    # Bagi files menjadi batch maksimal 10 file
    for i in range(0, len(files_data), batch_size):
        # Check cancel flag sebelum setiap batch
        if user_id:
            from .user_isolation import is_user_cancelled
            if is_user_cancelled(user_id):
                logging.info(f"User {user_id} cancelled - stopping at batch {i//batch_size + 1}")
                break  # Stop semua batch selanjutnya

        batch = files_data[i:i+batch_size]
        batch_success = False

        for attempt in range(1, max_retry + 1):
            # Check cancel flag di setiap retry
            if user_id:
                from .user_isolation import is_user_cancelled
                if is_user_cancelled(user_id):
                    logging.info(f"User {user_id} cancelled during retry - stopping batch")
                    batch_success = True  # Mark as success to avoid fallback
                    break

            try:
                # Buat media group untuk batch ini
                media_group = []
                for file_path, filename in batch:
                    media_group.append(InputMediaDocument(
                        media=FSInputFile(file_path, filename=filename)
                    ))

                # Kirim sebagai media group
                await message.answer_media_group(media_group)
                batch_success = True
                success_count += len(batch)
                logging.info(f"Berhasil kirim batch {len(batch)} file sebagai media group")

                # Jeda 1 detik antar batch untuk menghindari rate limiting
                if i + batch_size < len(files_data):  # Jika masih ada batch selanjutnya
                    await asyncio.sleep(1)
                break

            except TelegramRetryAfter as e:
                # Rate limiting - tunggu sesuai yang diminta Telegram
                wait_time = e.retry_after + 1  # Tambah 1 detik untuk safety
                logging.warning(f"Rate limit hit on media group, waiting {wait_time} seconds")
                await asyncio.sleep(wait_time)
                # Jangan hitung sebagai attempt yang gagal untuk rate limit
                continue

            except (asyncio.TimeoutError, TelegramNetworkError):
                if attempt == max_retry:
                    logging.error(f"Gagal kirim batch setelah {max_retry} percobaan, fallback ke individual")
                    # Fallback: kirim satu per satu dengan delay antar file
                    for idx, (file_path, filename) in enumerate(batch):
                        # Check cancel flag untuk setiap file individual
                        if user_id:
                            from .user_isolation import is_user_cancelled
                            if is_user_cancelled(user_id):
                                logging.info(f"User {user_id} cancelled during individual fallback")
                                break

                        if await retry_send_document(message, file_path, filename, max_retry, delay, user_id):
                            success_count += 1
                        # Jeda kecil antar file individual untuk menghindari rate limit
                        if idx < len(batch) - 1:  # Jika bukan file terakhir
                            await asyncio.sleep(0.5)
                    batch_success = True
                    break
                await asyncio.sleep(delay)

            except Exception as e:
                logging.error(f"Error kirim media group: {e}, fallback ke individual")
                # Fallback: kirim satu per satu dengan delay antar file
                for idx, (file_path, filename) in enumerate(batch):
                    # Check cancel flag untuk setiap file individual
                    if user_id:
                        from .user_isolation import is_user_cancelled
                        if is_user_cancelled(user_id):
                            logging.info(f"User {user_id} cancelled during exception fallback")
                            break

                    if await retry_send_document(message, file_path, filename, max_retry, delay, user_id):
                        success_count += 1
                    # Jeda kecil antar file individual untuk menghindari rate limit
                    if idx < len(batch) - 1:  # Jika bukan file terakhir
                        await asyncio.sleep(0.5)
                batch_success = True
                break

        if not batch_success:
            logging.error(f"Gagal kirim batch {len(batch)} file")

    # Hapus pesan gagal kirim - silent fail
    if success_count < total_files:
        return False

    return True

async def send_files_with_sending_mode(message, files_to_send, user_id: int, send_mode: str = "individual"):
    """
    Kirim file dengan sending mode tracking dan cancel support.
    Bot tidak akan merespons perintah lain saat dalam sending mode.

    Args:
        message: Message object dari aiogram
        files_to_send: List of tuples (file_path, filename)
        user_id: ID user Telegram
        send_mode: "group" atau "individual"

    Returns:
        tuple: (success: bool, sent_count: int, total_count: int, cancelled: bool)
    """
    if not files_to_send:
        return True, 0, 0, False

    # Import functions
    from .user_isolation import set_user_sending_mode, set_user_cancel_flag, is_user_cancelled

    total_files = len(files_to_send)
    sent_count = 0
    cancelled = False

    try:
        # Clear cancel flag dan set sending mode
        await set_user_cancel_flag(user_id, False)
        await set_user_sending_mode(user_id, True)
        logging.info(f"User {user_id} entering sending mode for {total_files} files")

        # Kirim file berdasarkan mode
        if send_mode == "group" and len(files_to_send) > 1:
            # Mode group: kirim berkelompok
            success = await retry_send_documents_group(message, files_to_send, user_id=user_id)
            if success and not is_user_cancelled(user_id):
                sent_count = total_files
            else:
                cancelled = is_user_cancelled(user_id)
        else:
            # Mode individual atau hanya 1 file
            for idx, (file_path, filename) in enumerate(files_to_send):
                # Check cancel flag sebelum setiap file
                if is_user_cancelled(user_id):
                    cancelled = True
                    logging.info(f"User {user_id} cancelled at file {idx+1}/{total_files}")
                    break

                if await retry_send_document(message, file_path, filename, user_id=user_id):
                    sent_count += 1
                else:
                    # Jika gagal kirim dan bukan karena cancel, tetap lanjut
                    if not is_user_cancelled(user_id):
                        continue
                    else:
                        cancelled = True
                        break

                # Jeda kecil antar file
                if idx < len(files_to_send) - 1:
                    await asyncio.sleep(0.5)

        # Kirim pesan hasil dengan keyboard restore
        from .cancel_keyboard import get_normal_keyboard

        if cancelled:
            # Hanya satu pesan cancel dengan keyboard normal - DENGAN RETRY
            for attempt in range(3):
                try:
                    await message.answer("❌ Pengiriman dibatalkan.", reply_markup=get_normal_keyboard())
                    break
                except TelegramRetryAfter as e:
                    if attempt < 2:
                        await asyncio.sleep(e.retry_after + 1)
        elif sent_count == total_files:
            # File berhasil dikirim semua - DENGAN RETRY
            for attempt in range(3):
                try:
                    await message.answer("📤 File berhasil dikirim!", reply_markup=get_normal_keyboard())
                    break
                except TelegramRetryAfter as e:
                    if attempt < 2:
                        await asyncio.sleep(e.retry_after + 1)
        # Untuk kasus partial fail, tetap restore keyboard dengan pesan error - DENGAN RETRY
        else:
            for attempt in range(3):
                try:
                    await message.answer("❌ Gagal mengirim file. Silakan coba lagi.", reply_markup=get_normal_keyboard())
                    break
                except TelegramRetryAfter as e:
                    if attempt < 2:
                        await asyncio.sleep(e.retry_after + 1)

        return sent_count == total_files, sent_count, total_files, cancelled

    except Exception as e:
        logging.error(f"Error in sending mode for user {user_id}: {e}")
        # Restore keyboard jika terjadi error dengan pesan - DENGAN RETRY
        from .cancel_keyboard import get_normal_keyboard
        for attempt in range(3):
            try:
                await message.answer("❌ Terjadi kesalahan saat mengirim file. Silakan coba lagi.", reply_markup=get_normal_keyboard())
                break
            except TelegramRetryAfter as e:
                if attempt < 2:
                    await asyncio.sleep(e.retry_after + 1)
            except:
                break  # Error lain, stop retry
        return False, sent_count, total_files, cancelled
    finally:
        # Selalu unset sending mode dan clear cancel flag
        try:
            await set_user_sending_mode(user_id, False)
            await set_user_cancel_flag(user_id, False)
            # logging.info(f"User {user_id} exited sending mode")  # Reduced logging - sudah ada di user_isolation.py
        except Exception as e:
            logging.error(f"Error cleaning up sending mode for user {user_id}: {e}")