import logging
import os
import time
import aiofiles
import asyncio
import re
from aiogram import Router, types, F, Bot
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from aiogram.filters import Command
from utils.retry_send import retry_send_document
from utils.user_isolation import with_user_isolation, is_user_busy
from utils.user_directories import get_user_file_path
from utils.smart_logging import log_user_smart, log_bot, flush_user_logs

router = Router()

class RenameCtcStates(StatesGroup):
    waiting_files = State()
    waiting_done = State()
    waiting_old_name = State()
    waiting_new_name = State()

# Gunakan smart logging dari utils
def log_user(message: types.Message):
    log_user_smart(message)

def case_insensitive_search(content, search_name):
    """
    Cari nama kontak dengan case-insensitive.
    Returns: True jika ditemukan, False jika tidak
    """
    # Escape special regex characters dalam search_name
    escaped_name = re.escape(search_name)
    # Cari dengan case-insensitive
    pattern = re.compile(escaped_name, re.IGNORECASE)
    return bool(pattern.search(content))

def case_preserving_replace(content, old_name, new_name):
    """
    Ganti nama kontak dengan case-insensitive search tapi preserve case dari new_name.
    Returns: content yang sudah diganti
    """
    # Escape special regex characters dalam old_name
    escaped_old = re.escape(old_name)
    # Buat pattern case-insensitive
    pattern = re.compile(escaped_old, re.IGNORECASE)

    # Ganti semua occurrence dengan new_name (preserve case dari new_name)
    def replace_func(match):
        return new_name

    return pattern.sub(replace_func, content)

# Handler global untuk perintah lain agar bisa membatalkan proses ini
@router.message(Command("start"), F.chat.type == "private")
async def start_from_renamectc(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import start_handler
    await start_handler(message, state)

@router.message(Command("to_vcf"), F.chat.type == "private")
async def to_vcf_from_renamectc(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_vcf import to_vcf_start
    await to_vcf_start(message, state)

@router.message(Command("to_txt"), F.chat.type == "private")
async def to_txt_from_renamectc(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_txt import to_txt_start
    await to_txt_start(message, state)

@router.message(Command("admin"), F.chat.type == "private")
async def admin_from_renamectc(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.admin import admin_start
    await admin_start(message, state)

@router.message(Command("manual"), F.chat.type == "private")
async def manual_from_renamectc(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.manual import manual_start
    await manual_start(message, state)

@router.message(Command("add"), F.chat.type == "private")
async def add_from_renamectc(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.add import add_start
    await add_start(message, state)

@router.message(Command("delete"), F.chat.type == "private")
async def delete_from_renamectc(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.delete import delete_start
    await delete_start(message, state)

@router.message(Command("renamectc"), F.chat.type == "private")
async def renamectc_global(message: types.Message, state: FSMContext):
    await state.clear()
    await renamectc_start(message, state)

@router.message(Command("renamefile"), F.chat.type == "private")
async def renamefile_from_renamectc(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamefile import renamefile_start
    await renamefile_start(message, state)

@router.message(Command("merge"), F.chat.type == "private")
async def merge_from_renamectc(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.merge import merge_start
    await merge_start(message, state)

@router.message(Command("split"), F.chat.type == "private")
async def split_from_renamectc(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.split import split_start
    await split_start(message, state)

@router.message(Command("count"), F.chat.type == "private")
async def count_from_renamectc(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.count import count_start
    await count_start(message, state)

@router.message(Command("nodup"), F.chat.type == "private")
async def nodup_from_renamectc(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.nodup import nodup_start
    await nodup_start(message, state)

@router.message(Command("getname"), F.chat.type == "private")
async def getname_from_renamectc(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.getname import getname_start
    await getname_start(message, state)

@router.message(Command("generate"), F.chat.type == "private")
async def generate_from_renamectc(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.generate import generate_start
    await generate_start(message, state)

@router.message(Command("setting"), F.chat.type == "private")
async def setting_from_renamectc(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.hide_menu import hide_menu_start
    await hide_menu_start(message, state)

@router.message(Command("help"), F.chat.type == "private")
async def help_from_renamectc(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import help_handler
    await help_handler(message, state)

async def renamectc_start(message: types.Message, state: FSMContext):
    # Silent force release semaphore jika user stuck
    if is_user_busy(message.from_user.id):
        from utils.user_isolation import force_release_user_lock
        force_release_user_lock(message.from_user.id)
        await state.clear()
        # Lanjut ke processing normal tanpa pesan pembatalan

    log_user(message)

    # Flush any pending logs dari command sebelumnya
    flush_user_logs(message.from_user.id)
    bot_msg = "Kirim file .vcf yang mau diganti nama kontaknya"
    await message.answer(bot_msg)
    log_bot(bot_msg)
    await state.set_state(RenameCtcStates.waiting_files)
    await state.update_data(files=[], logs=[], file_error=False)

@router.message(RenameCtcStates.waiting_files, F.document, F.chat.type == "private")
async def renamectc_receive_file(message: types.Message, state: FSMContext, bot: Bot):
    log_user(message)
    file = message.document
    _, ext = os.path.splitext(file.file_name.lower())
    allowed_ext = [".vcf"]

    data = await state.get_data()
    if data.get("file_error"):
        return

    if ext not in allowed_ext:
        await state.update_data(files=[], logs=[], file_error=True)
        bot_msg = "❌ Hanya file .vcf yang didukung!\nKetik /renamectc untuk mulai ulang."
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return

    try:
        files = data.get("files", [])
        logs = data.get("logs", [])
        filename, ext_real = os.path.splitext(file.file_name)
        timestamp = int(time.time() * 1000)
        unique_name = f"{filename}_{timestamp}{ext_real}"
        file_path = get_user_file_path(message.from_user.id, unique_name)
        await bot.download(file, destination=file_path)
        files.append((file_path, file.file_name, message.message_id))
        logs.append((message.message_id, f"bot: File {file.file_name} diterima"))
        await state.update_data(files=files, logs=logs)
        state_now = await state.get_data()
        if len(state_now.get("files", [])) == 1 and not state_now.get("file_error"):
            bot_msg = "✅ File diterima. Ketik /done untuk lanjut."
            await message.answer(bot_msg)
            log_bot(bot_msg)
    except Exception as e:
        err_msg = "⚠️ Gagal menerima file. Coba lagi."
        log_bot(err_msg)
        logging.error(f"user: kirim file {file.file_name if 'file' in locals() else '[unknown]'} error: {e}")
        await message.answer(err_msg)

@router.message(RenameCtcStates.waiting_files, Command("done"), F.chat.type == "private")
async def renamectc_done(message: types.Message, state: FSMContext):
    log_user(message)
    data = await state.get_data()
    files = data.get("files", [])
    logs = data.get("logs", [])
    if not files:
        bot_msg = "⚠️ Belum ada file. Kirim file dulu."
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return
    files = sorted(files, key=lambda x: x[2])
    logs = sorted(logs, key=lambda x: x[0])
    await state.update_data(files=files, logs=logs)
    # Summary log untuk file reception
    total_files = len(files)
    if total_files > 0:
        logging.info(f"bot: {total_files} file diterima untuk renamectc")
    bot_msg = "Masukkan nama kontak yang mau diganti:"
    await message.answer(bot_msg)
    log_bot(bot_msg)
    await state.set_state(RenameCtcStates.waiting_old_name)

@router.message(RenameCtcStates.waiting_old_name, F.chat.type == "private")
async def renamectc_receive_old_name(message: types.Message, state: FSMContext):
    if message.text.strip().startswith("/"):
        await state.clear()
        return

    log_user(message)
    old_name = message.text.strip()
    if not old_name:
        bot_msg = "Nama kontak tidak boleh kosong. Masukkan nama kontak yang mau diganti:"
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return
    await state.update_data(old_name=old_name)
    # Cek apakah nama kontak ditemukan di salah satu file (case-insensitive)
    data = await state.get_data()
    files = data.get("files", [])
    found = False
    found_files = []

    for file_path, original_filename, _ in files:
        try:
            # Gunakan deteksi encoding untuk membaca file
            from utils.file import read_file_with_encoding_detection
            lines, encoding_used = await read_file_with_encoding_detection(file_path)
            content = "\n".join(lines)

            if case_insensitive_search(content, old_name):
                found = True
                found_files.append(original_filename)
        except Exception as e:
            logging.error(f"Error membaca file {file_path}: {e}")
            # Fallback ke encoding utf-8
            try:
                async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
                    content = await f.read()
                if case_insensitive_search(content, old_name):
                    found = True
                    found_files.append(original_filename)
            except Exception as e2:
                logging.error(f"Fallback encoding juga gagal untuk {file_path}: {e2}")

    if not found:
        bot_msg = f"❌ '{old_name}' tidak ditemukan."
        await message.answer(bot_msg)
        log_bot(bot_msg)
        await state.clear()
        return

    bot_msg = f"'{old_name}' ditemukan\nMau diganti jadi apa?"
    await message.answer(bot_msg)
    log_bot(bot_msg)
    await state.set_state(RenameCtcStates.waiting_new_name)

@router.message(RenameCtcStates.waiting_new_name, F.chat.type == "private")
async def renamectc_receive_new_name(message: types.Message, state: FSMContext):
    if message.text.strip().startswith("/"):
        await state.clear()
        return

    log_user(message)

    # Flush pending file upload logs sebelum proses
    flush_user_logs(message.from_user.id)

    new_name = message.text.strip()
    if not new_name:
        bot_msg = "Nama baru tidak boleh kosong. Masukkan nama baru:"
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return

    await state.update_data(new_name=new_name)
    await process_renamectc_with_isolation(message, state)

async def process_renamectc_with_isolation(message, state):
    """
    Wrapper function untuk process_renamectc dengan user isolation.
    """
    from aiogram.exceptions import TelegramRetryAfter
    user_id = message.from_user.id

    # Jalankan process_renamectc dengan user isolation
    success, result = await with_user_isolation(user_id, process_renamectc, message, state)

    if not success:
        # User sedang busy
        bot_msg = f"⏳ {result}"
        try:
            await message.answer(bot_msg)
            log_bot(bot_msg)
        except TelegramRetryAfter:
            log_bot(bot_msg)

async def process_renamectc(message, state):
    """
    Fungsi utama untuk memproses renamectc.
    """
    data = await state.get_data()
    files = data.get("files", [])
    old_name = data.get("old_name", "")
    new_name = data.get("new_name", "")
    file_paths_to_delete = []
    processed_files = []
    unchanged_files = []

    try:
        for file_path, original_filename, _ in files:
            try:
                # Gunakan deteksi encoding untuk membaca file
                from utils.file import read_file_with_encoding_detection
                lines, encoding_used = await read_file_with_encoding_detection(file_path)
                content = "\n".join(lines)

                # Cek apakah nama ditemukan (case-insensitive)
                if case_insensitive_search(content, old_name):
                    # Ganti dengan case-preserving replacement
                    new_content = case_preserving_replace(content, old_name, new_name)
                    output_name = original_filename
                    output_path = get_user_file_path(message.from_user.id, output_name)

                    # Tulis file dengan encoding yang sesuai
                    from utils.format import write_text_file
                    await write_text_file(output_path, new_content, encoding=encoding_used)

                    await retry_send_document(message, output_path, output_name)
                    # log_bot(f"kirim file {output_name} (nama '{old_name}' diganti jadi '{new_name}')")  # Dikurangi untuk mengurangi spam log
                    file_paths_to_delete.append(output_path)
                    processed_files.append(original_filename)
                else:
                    # Kirim balik file tanpa perubahan
                    await retry_send_document(message, file_path, original_filename)
                    # log_bot(f"kirim file {original_filename} (tidak ada perubahan)")  # Dikurangi untuk mengurangi spam log
                    unchanged_files.append(original_filename)

            except Exception as e:
                logging.error(f"Error memproses file {file_path}: {e}")
                # Fallback ke encoding utf-8
                try:
                    async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
                        content = await f.read()

                    if case_insensitive_search(content, old_name):
                        new_content = case_preserving_replace(content, old_name, new_name)
                        output_name = original_filename
                        output_path = get_user_file_path(message.from_user.id, output_name)

                        from utils.format import write_text_file
                        await write_text_file(output_path, new_content)

                        await retry_send_document(message, output_path, output_name)
                        # log_bot(f"kirim file {output_name} (nama '{old_name}' diganti jadi '{new_name}' - fallback encoding)")  # Dikurangi untuk mengurangi spam log
                        file_paths_to_delete.append(output_path)
                        processed_files.append(original_filename)
                    else:
                        await retry_send_document(message, file_path, original_filename)
                        # log_bot(f"kirim file {original_filename} (tidak ada perubahan - fallback encoding)")  # Dikurangi untuk mengurangi spam log
                        unchanged_files.append(original_filename)
                except Exception as e2:
                    logging.error(f"Fallback encoding juga gagal untuk {file_path}: {e2}")
                    # Kirim file asli jika semua gagal
                    await retry_send_document(message, file_path, original_filename)
                    # log_bot(f"kirim file {original_filename} (error, file asli dikirim)")  # Dikurangi untuk mengurangi spam log
                    unchanged_files.append(original_filename)

        # Summary log untuk renamectc
        total_processed = len(processed_files)
        total_unchanged = len(unchanged_files)
        if total_processed > 0:
            log_bot(f"Berhasil kirim {total_processed} file dengan nama kontak diganti '{old_name}' → '{new_name}'")
        if total_unchanged > 0:
            log_bot(f"Berhasil kirim {total_unchanged} file tanpa perubahan")

        bot_msg = "📤 File sudah dikirim!"
        await message.answer(bot_msg)
        log_bot(bot_msg)
    except Exception as e:
        err_msg = f"❌ Gagal proses file. Ketik /renamectc untuk ulang.\n{e}"
        logging.error(err_msg)
        log_bot(err_msg)
        await message.answer(err_msg)
    finally:
        async def remove_file(path):
            try:
                if os.path.exists(path):
                    os.remove(path)
                    # logging.info(f"File hasil dihapus: {path}")
            except Exception as e:
                logging.error(f"Gagal hapus file hasil: {path} ({e})")
        await asyncio.gather(*(remove_file(path) for path in file_paths_to_delete))
        await state.clear()