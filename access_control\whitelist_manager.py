"""
Whitelist Manager - Core logic untuk manage whitelist users
"""

import os
import logging
import aiofiles
from typing import List, Set, Optional, Tuple

WHITELIST_FILE = "access_control/database/whitelist.txt"

async def ensure_whitelist_file():
    """Pastikan file whitelist ada"""
    os.makedirs(os.path.dirname(WHITELIST_FILE), exist_ok=True)
    if not os.path.exists(WHITELIST_FILE):
        async with aiofiles.open(WHITELIST_FILE, "w", encoding="utf-8") as f:
            await f.write("# Whitelist User IDs\n# Satu user ID per baris\n")

async def load_whitelist() -> Set[int]:
    """Load whitelist dari file"""
    await ensure_whitelist_file()
    whitelist = set()
    
    try:
        async with aiofiles.open(WHITELIST_FILE, "r", encoding="utf-8") as f:
            lines = await f.readlines()
            
        for line in lines:
            line = line.strip()
            if line and not line.startswith("#") and line.isdigit():
                whitelist.add(int(line))
                
    except Exception as e:
        logging.error(f"Error loading whitelist: {e}")
        
    return whitelist

async def save_whitelist(whitelist: Set[int]):
    """Save whitelist ke file"""
    await ensure_whitelist_file()
    
    try:
        async with aiofiles.open(WHITELIST_FILE, "w", encoding="utf-8") as f:
            await f.write("# Whitelist User IDs\n")
            await f.write("# Satu user ID per baris\n")
            await f.write("# Auto-generated, jangan edit manual\n\n")
            
            # Sort user IDs untuk konsistensi
            for user_id in sorted(whitelist):
                await f.write(f"{user_id}\n")
                
    except Exception as e:
        logging.error(f"Error saving whitelist: {e}")
        raise

async def is_user_whitelisted(user_id: int) -> bool:
    """Cek apakah user ada di whitelist"""
    whitelist = await load_whitelist()
    return user_id in whitelist

async def add_user_to_whitelist(user_id: int) -> bool:
    """Tambah user ke whitelist"""
    try:
        whitelist = await load_whitelist()
        
        if user_id in whitelist:
            return False  # User sudah ada
            
        whitelist.add(user_id)
        await save_whitelist(whitelist)
        return True
        
    except Exception as e:
        logging.error(f"Error adding user {user_id} to whitelist: {e}")
        return False

async def remove_user_from_whitelist(user_id: int) -> bool:
    """Hapus user dari whitelist"""
    try:
        whitelist = await load_whitelist()
        
        if user_id not in whitelist:
            return False  # User tidak ada
            
        whitelist.remove(user_id)
        await save_whitelist(whitelist)
        return True
        
    except Exception as e:
        logging.error(f"Error removing user {user_id} from whitelist: {e}")
        return False

async def get_whitelist_count() -> int:
    """Get jumlah user di whitelist"""
    whitelist = await load_whitelist()
    return len(whitelist)

async def get_all_whitelisted_users() -> List[int]:
    """Get semua user ID yang di whitelist"""
    whitelist = await load_whitelist()
    return sorted(list(whitelist))

# User resolution functions
async def resolve_user_from_text(text: str) -> Optional[Tuple[int, str, str]]:
    """
    Resolve user dari text input (username, nama, atau ID)
    Returns: (user_id, username, full_name) atau None jika tidak ditemukan
    """
    from .user_history import find_user_by_username, find_user_by_name, find_user_by_id

    text = text.strip()

    # Jika input adalah angka (User ID)
    if text.isdigit():
        user_id = int(text)
        # Coba cari di database untuk info lengkap
        user_info = await find_user_by_id(user_id)
        if user_info:
            username, full_name, found_user_id = user_info
            return (found_user_id, username, full_name)
        else:
            # User ID valid tapi belum ada di database
            return (user_id, "", f"User ID: {user_id}")

    # Jika dimulai dengan @ (Username)
    if text.startswith("@"):
        username = text[1:]  # Hapus @
        # Cari user berdasarkan username di database
        user_info = await find_user_by_username(username)
        if user_info:
            found_username, full_name, user_id = user_info
            return (user_id, found_username, full_name)
        # Jika tidak ditemukan, return None agar masuk kategori "not found"
        return None

    # Jika teks biasa (Nama pengguna)
    user_info = await find_user_by_name(text)
    if user_info:
        username, full_name, user_id = user_info
        return (user_id, username, full_name)
    # Jika tidak ditemukan, return None agar masuk kategori "not found"
    return None

# Fungsi-fungsi lama sudah dipindah ke user_history.py
