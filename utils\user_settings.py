import os
import logging

USER_SETTINGS_FILE = "management/database/user_settings.txt"

def get_user_setting(user_id: int, setting_name: str, default_value: str = None):
    """
    Ambil setting user berdasarkan user_id dan setting_name
    
    Args:
        user_id: ID user Telegram
        setting_name: nama setting (contoh: "to_vcf_naming")
        default_value: nilai default jika setting tidak ditemukan
    
    Returns:
        str: nilai setting atau default_value
    """
    if not os.path.exists(USER_SETTINGS_FILE):
        return default_value
    
    try:
        with open(USER_SETTINGS_FILE, "r", encoding="utf-8") as f:
            for line in f:
                if line.strip():
                    parts = line.strip().split("|")
                    if len(parts) >= 3:
                        file_user_id, file_setting_name, setting_value = parts[:3]
                        if file_user_id == str(user_id) and file_setting_name == setting_name:
                            return setting_value
    except Exception as e:
        logging.error(f"Error reading user settings: {e}")
    
    return default_value

def set_user_setting(user_id: int, setting_name: str, setting_value: str):
    """
    Set/update setting user
    
    Args:
        user_id: ID user Telegram
        setting_name: nama setting (contoh: "to_vcf_naming")
        setting_value: nilai setting (contoh: "beruntun" atau "per_file")
    
    Returns:
        bool: True jika berhasil, False jika gagal
    """
    os.makedirs("management/database", exist_ok=True)
    
    # Baca semua settings yang ada
    settings_data = {}
    if os.path.exists(USER_SETTINGS_FILE):
        try:
            with open(USER_SETTINGS_FILE, "r", encoding="utf-8") as f:
                for line in f:
                    if line.strip():
                        parts = line.strip().split("|")
                        if len(parts) >= 3:
                            file_user_id, file_setting_name, file_setting_value = parts[:3]
                            key = f"{file_user_id}|{file_setting_name}"
                            settings_data[key] = file_setting_value
        except Exception as e:
            logging.error(f"Error reading existing settings: {e}")
    
    # Update/tambah setting baru
    key = f"{user_id}|{setting_name}"
    settings_data[key] = setting_value
    
    # Simpan kembali semua settings
    try:
        with open(USER_SETTINGS_FILE, "w", encoding="utf-8") as f:
            for setting_key, value in settings_data.items():
                f.write(f"{setting_key}|{value}\n")
        return True
    except Exception as e:
        logging.error(f"Error saving user settings: {e}")
        return False

def get_to_vcf_naming_mode(user_id: int):
    """
    Ambil mode penamaan untuk fitur /to_vcf
    
    Args:
        user_id: ID user Telegram
    
    Returns:
        str: "beruntun" (default) atau "per_file"
    """
    return get_user_setting(user_id, "to_vcf_naming", "beruntun")

def set_to_vcf_naming_mode(user_id: int, mode: str):
    """
    Set mode penamaan untuk fitur /to_vcf

    Args:
        user_id: ID user Telegram
        mode: "beruntun" atau "per_file"

    Returns:
        bool: True jika berhasil, False jika gagal
    """
    if mode not in ["beruntun", "per_file"]:
        logging.error(f"Invalid naming mode: {mode}")
        return False

    return set_user_setting(user_id, "to_vcf_naming", mode)

def get_to_vcf_send_mode(user_id: int):
    """
    Ambil mode pengiriman untuk fitur /to_vcf

    Args:
        user_id: ID user Telegram

    Returns:
        str: "individual" (default) atau "group"
    """
    return get_user_setting(user_id, "to_vcf_send", "group")

def set_to_vcf_send_mode(user_id: int, mode: str):
    """
    Set mode pengiriman untuk fitur /to_vcf

    Args:
        user_id: ID user Telegram
        mode: "individual" atau "group"

    Returns:
        bool: True jika berhasil, False jika gagal
    """
    if mode not in ["individual", "group"]:
        logging.error(f"Invalid send mode: {mode}")
        return False

    return set_user_setting(user_id, "to_vcf_send", mode)

def get_to_txt_send_mode(user_id: int):
    """
    Ambil mode pengiriman untuk fitur /to_txt

    Args:
        user_id: ID user Telegram

    Returns:
        str: "individual" (default) atau "group"
    """
    return get_user_setting(user_id, "to_txt_send", "group")

def set_to_txt_send_mode(user_id: int, mode: str):
    """
    Set mode pengiriman untuk fitur /to_txt

    Args:
        user_id: ID user Telegram
        mode: "individual" atau "group"

    Returns:
        bool: True jika berhasil, False jika gagal
    """
    if mode not in ["individual", "group"]:
        logging.error(f"Invalid send mode: {mode}")
        return False

    return set_user_setting(user_id, "to_txt_send", mode)

def get_to_txt_format(user_id: int):
    """
    Ambil format output untuk fitur /to_txt

    Args:
        user_id: ID user Telegram

    Returns:
        str: "txt" (default) atau "xlsx"
    """
    return get_user_setting(user_id, "to_txt_format", "txt")

def set_to_txt_format(user_id: int, format_type: str):
    """
    Set format output untuk fitur /to_txt

    Args:
        user_id: ID user Telegram
        format_type: "txt" atau "xlsx"

    Returns:
        bool: True jika berhasil, False jika gagal
    """
    if format_type not in ["txt", "xlsx"]:
        logging.error(f"Invalid format type: {format_type}")
        return False

    return set_user_setting(user_id, "to_txt_format", format_type)
