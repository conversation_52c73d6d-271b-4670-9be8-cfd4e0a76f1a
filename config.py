import os
from dotenv import load_dotenv

load_dotenv()
BOT_TOKEN = os.getenv("TELEGRAM_TOKEN")

# Link tutorial
TUTORIAL_LINK = os.getenv("TUTORIAL_LINK")

# Admin configurations
ADMIN_USERNAMES = [u.strip() for u in os.getenv("ADMIN_USERNAMES", "").split(",") if u.strip()]
WHITELIST_ADMINS = [u.strip() for u in os.getenv("WHITELIST_ADMINS", "").split(",") if u.strip()]
CONTACT_ADMIN = os.getenv("CONTACT_ADMIN", "@admin")

def is_super_admin(username: str) -> bool:
    """Cek apakah user adalah super admin"""
    if not username:
        return False
    return username.lower() in [u.lower() for u in ADMIN_USERNAMES]

def is_whitelist_admin(username: str) -> bool:
    """Cek apakah user bisa manage whitelist"""
    if not username:
        return False
    # Super admin otomatis bisa manage whitelist
    if is_super_admin(username):
        return True
    # Atau cek whitelist admin khusus
    return username.lower() in [u.lower() for u in WHITELIST_ADMINS]