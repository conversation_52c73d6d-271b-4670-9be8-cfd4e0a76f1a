import os
import logging
import shutil
import time
from typing import Optional

def get_user_data_dir(user_id: int) -> str:
    """
    Ambil atau buat direktori data untuk user tertentu.
    
    Args:
        user_id: ID user Telegram
        
    Returns:
        str: Path ke direktori user (contoh: "data/user_12345")
    """
    user_dir = f"data/user_{user_id}"
    
    # Buat direktori jika belum ada
    try:
        os.makedirs(user_dir, exist_ok=True)
        return user_dir
    except Exception as e:
        logging.error(f"Failed to create user directory {user_dir}: {e}")
        # Fallback ke direktori data umum
        os.makedirs("data", exist_ok=True)
        return "data"

def get_user_file_path(user_id: int, filename: str) -> str:
    """
    Ambil path lengkap untuk file dalam direktori user.
    
    Args:
        user_id: ID user Telegram
        filename: Nama file
        
    Returns:
        str: Path lengkap file (contoh: "data/user_12345/kontak.vcf")
    """
    user_dir = get_user_data_dir(user_id)
    return os.path.join(user_dir, filename)

def cleanup_user_directory(user_id: int, keep_recent_files: int = 0) -> bool:
    """
    Bersihkan direktori user dari file-file lama.
    
    Args:
        user_id: ID user Telegram
        keep_recent_files: Jumlah file terbaru yang tetap disimpan (0 = hapus semua)
        
    Returns:
        bool: True jika berhasil cleanup
    """
    user_dir = f"data/user_{user_id}"
    
    if not os.path.exists(user_dir):
        return True
    
    try:
        if keep_recent_files == 0:
            # Hapus semua file dalam direktori
            for filename in os.listdir(user_dir):
                file_path = os.path.join(user_dir, filename)
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    logging.info(f"Deleted user file: {file_path}")
        else:
            # Hapus file lama, simpan yang terbaru
            files = []
            for filename in os.listdir(user_dir):
                file_path = os.path.join(user_dir, filename)
                if os.path.isfile(file_path):
                    files.append((file_path, os.path.getmtime(file_path)))
            
            # Sort berdasarkan waktu modifikasi (terbaru dulu)
            files.sort(key=lambda x: x[1], reverse=True)
            
            # Hapus file yang lebih dari keep_recent_files
            for file_path, _ in files[keep_recent_files:]:
                os.remove(file_path)
                logging.info(f"Deleted old user file: {file_path}")
        
        return True
    except Exception as e:
        logging.error(f"Failed to cleanup user directory {user_dir}: {e}")
        return False

def cleanup_empty_user_directories(max_age_days: int = 7) -> int:
    """
    Hapus direktori user yang kosong dan sudah lama tidak digunakan.

    Args:
        max_age_days: Maksimal umur direktori kosong dalam hari

    Returns:
        int: Jumlah direktori yang dihapus
    """
    
    data_dir = "data"
    if not os.path.exists(data_dir):
        return 0
    
    deleted_count = 0
    current_time = time.time()
    max_age_seconds = max_age_days * 24 * 60 * 60
    
    try:
        for item in os.listdir(data_dir):
            if item.startswith("user_"):
                user_dir_path = os.path.join(data_dir, item)
                
                if os.path.isdir(user_dir_path):
                    # Cek apakah direktori kosong
                    if not os.listdir(user_dir_path):
                        # Cek umur direktori
                        dir_age = current_time - os.path.getmtime(user_dir_path)
                        
                        if dir_age > max_age_seconds:
                            shutil.rmtree(user_dir_path)
                            # logging.info(f"Deleted empty user directory: {user_dir_path}")  # Disabled untuk mengurangi noise
                            deleted_count += 1
    except Exception as e:
        logging.error(f"Failed to cleanup empty user directories: {e}")
    
    return deleted_count

def get_user_directory_size(user_id: int) -> int:
    """
    Hitung ukuran total direktori user dalam bytes.
    
    Args:
        user_id: ID user Telegram
        
    Returns:
        int: Ukuran direktori dalam bytes
    """
    user_dir = f"data/user_{user_id}"
    
    if not os.path.exists(user_dir):
        return 0
    
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(user_dir):
            for filename in filenames:
                file_path = os.path.join(dirpath, filename)
                if os.path.exists(file_path):
                    total_size += os.path.getsize(file_path)
    except Exception as e:
        logging.error(f"Failed to calculate user directory size for {user_id}: {e}")
    
    return total_size

def migrate_legacy_files_to_user_dirs():
    """
    Migrasi file-file lama dari direktori data umum ke direktori per user.
    Fungsi ini untuk backward compatibility.
    
    Note: Karena file lama tidak ada info user_id, file akan dipindah ke folder khusus.
    """
    data_dir = "data"
    legacy_dir = "data/legacy_files"
    
    if not os.path.exists(data_dir):
        return
    
    try:
        # Buat folder legacy jika belum ada
        os.makedirs(legacy_dir, exist_ok=True)
        
        moved_count = 0
        for item in os.listdir(data_dir):
            item_path = os.path.join(data_dir, item)
            
            # Skip direktori user dan legacy
            if os.path.isdir(item_path) and (item.startswith("user_") or item == "legacy_files"):
                continue
            
            # Pindahkan file ke folder legacy
            if os.path.isfile(item_path):
                legacy_file_path = os.path.join(legacy_dir, item)
                
                # Jika file sudah ada di legacy, tambah timestamp
                if os.path.exists(legacy_file_path):
                    timestamp = int(time.time())
                    name, ext = os.path.splitext(item)
                    legacy_file_path = os.path.join(legacy_dir, f"{name}_{timestamp}{ext}")
                
                shutil.move(item_path, legacy_file_path)
                logging.info(f"Migrated legacy file: {item} -> {legacy_file_path}")
                moved_count += 1
        
        if moved_count > 0:
            logging.info(f"Migrated {moved_count} legacy files to {legacy_dir}")
    
    except Exception as e:
        logging.error(f"Failed to migrate legacy files: {e}")
