import logging
import os
import aiofiles
import asyncio
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from aiogram.filters import Command
from utils.retry_send import retry_send_document
from utils.user_isolation import is_user_busy
from utils.user_directories import get_user_file_path
from utils.phone_converter import convert_08_to_62
import time


router = Router()

class ManualStates(StatesGroup):
    waiting_numbers = State()
    waiting_contact_name = State()
    waiting_filename = State()

def log_user(message: types.Message):
    # logging.info(f"user: {message.text}")  # Disabled
    pass

def log_bot(text: str):
    # logging.info(f"bot: {text}")  # Disabled
    pass

def clean_and_validate_number(line):
    """
    Bersihkan dan validasi nomor dengan konversi 08→628:
    - ✅ KONVERSI 08→628: Convert 08xxx ke 628xxx di awal nomor
    - Hapus semua karakter kecuali angka dan +
    - <PERSON><PERSON> ada + di awal, per<PERSON><PERSON><PERSON>, sisanya hanya angka
    - Jika tidak ada + di awal, tambahkan + di depan
    - Nomor valid: minimal 8 digit angka (tanpa +)
    - Return nomor valid dengan + di depan, atau None jika tidak valid
    """
    import re
    line = line.strip()
    if not line:
        return None

    # ✅ KONVERSI 08→628: Convert sebelum processing lainnya
    line = convert_08_to_62(line)

    if line.startswith("+"):
        nomor = "+" + re.sub(r"[^\d]", "", line[1:])
    else:
        nomor = re.sub(r"[^\d]", "", line)
        nomor = "+" + nomor
    digit_count = len(re.sub(r"[^\d]", "", nomor))
    if digit_count < 8:
        return None
    return nomor

def extract_valid_numbers_from_lines(lines):
    numbers = []
    conversion_count = 0

    for line in lines:
        original_line = line.strip()
        nomor = clean_and_validate_number(line)
        if nomor:
            numbers.append(nomor)
            # Count conversions untuk logging
            if original_line.startswith('08') and not original_line.startswith('+08'):
                conversion_count += 1

    # Log konversi jika ada
    if conversion_count > 0:
        logging.info(f"/manual: Converted {conversion_count} numbers from 08->628")

    return numbers

# Fungsi write_vcf_file dan create_vcf_content sudah dipindah ke utils.format

# Handler global untuk perintah lain agar bisa membatalkan proses ini

@router.message(Command("to_vcf"), F.chat.type == "private")
async def to_vcf_from_manual(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_vcf import to_vcf_start
    await to_vcf_start(message, state)

@router.message(Command("to_txt"), F.chat.type == "private")
async def to_txt_from_manual(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.to_txt import to_txt_start
    await to_txt_start(message, state)

@router.message(Command("start"), F.chat.type == "private")
async def start_from_manual(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import start_handler
    await start_handler(message, state)

@router.message(Command("help"), F.chat.type == "private")
async def help_from_manual(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.start import help_handler
    await help_handler(message, state)

@router.message(Command("admin"), F.chat.type == "private")
async def admin_from_manual(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.admin import admin_start
    await admin_start(message, state)

@router.message(Command("add"), F.chat.type == "private")
async def add_from_manual(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.add import add_start
    await add_start(message, state)

@router.message(Command("delete"), F.chat.type == "private")
async def delete_from_manual(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.delete import delete_start
    await delete_start(message, state)

@router.message(Command("renamectc"), F.chat.type == "private")
async def renamectc_from_manual(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamectc import renamectc_start
    await renamectc_start(message, state)

@router.message(Command("renamefile"), F.chat.type == "private")
async def renamefile_from_manual(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.renamefile import renamefile_start
    await renamefile_start(message, state)

@router.message(Command("merge"), F.chat.type == "private")
async def merge_from_manual(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.merge import merge_start
    await merge_start(message, state)

@router.message(Command("split"), F.chat.type == "private")
async def split_from_manual(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.split import split_start
    await split_start(message, state)

@router.message(Command("count"), F.chat.type == "private")
async def count_from_manual(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.count import count_start
    await count_start(message, state)

@router.message(Command("nodup"), F.chat.type == "private")
async def nodup_from_manual(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.nodup import nodup_start
    await nodup_start(message, state)

@router.message(Command("getname"), F.chat.type == "private")
async def getname_from_manual(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.getname import getname_start
    await getname_start(message, state)

@router.message(Command("generate"), F.chat.type == "private")
async def generate_from_manual(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.generate import generate_start
    await generate_start(message, state)

@router.message(Command("setting"), F.chat.type == "private")
async def setting_from_manual(message: types.Message, state: FSMContext):
    await state.clear()
    from handlers.hide_menu import hide_menu_start
    await hide_menu_start(message, state)

#Handler utama
@router.message(Command("manual"), F.chat.type == "private")
async def manual_start(message: types.Message, state: FSMContext):


    # Cek apakah user sedang busy processing
    if is_user_busy(message.from_user.id):
        bot_msg = "⏳ Anda sedang memproses file lain. Tunggu hingga selesai atau ketik /manual untuk membatalkan proses sebelumnya."
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return

    log_user(message)
    bot_msg = "Masukkan nomor:"
    await message.answer(bot_msg)
    log_bot(bot_msg)
    await state.set_state(ManualStates.waiting_numbers)

@router.message(ManualStates.waiting_numbers, F.chat.type == "private")
async def manual_receive_numbers(message: types.Message, state: FSMContext):
    if message.text.strip().startswith("/"):
        await state.clear()
        return

    log_user(message)
    lines = message.text.strip().splitlines()
    numbers = extract_valid_numbers_from_lines(lines)
    numbers = list(dict.fromkeys(numbers))  # Hapus duplikat, urutan tetap
    if not numbers:
        bot_msg = "Nomor tidak valid. Masukkan ulang nomor (pisahkan per baris):"
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return
    await state.update_data(numbers=numbers)
    bot_msg = "Masukkan nama kontak:"
    await message.answer(bot_msg)
    log_bot(bot_msg)
    await state.set_state(ManualStates.waiting_contact_name)

@router.message(ManualStates.waiting_contact_name, F.chat.type == "private")
async def manual_receive_contact_name(message: types.Message, state: FSMContext):
    if message.text.strip().startswith("/"):
        await state.clear()
        return

    log_user(message)
    contact_name = message.text.strip()
    if not contact_name:
        bot_msg = "Nama kontak tidak boleh kosong. Masukkan nama kontak:"
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return
    await state.update_data(contact_name=contact_name)
    bot_msg = "Masukkan nama file:"
    await message.answer(bot_msg)
    log_bot(bot_msg)
    await state.set_state(ManualStates.waiting_filename)

@router.message(ManualStates.waiting_filename, F.chat.type == "private")
async def manual_receive_filename(message: types.Message, state: FSMContext):
    if message.text.strip().startswith("/"):
        await state.clear()
        return

    log_user(message)
    filename = message.text.strip()
    if not filename:
        bot_msg = "Nama file tidak boleh kosong. Masukkan nama file:"
        await message.answer(bot_msg)
        log_bot(bot_msg)
        return

    data = await state.get_data()
    numbers = data.get("numbers", [])
    contact_name = data.get("contact_name", "Kontak")

    # Penamaan kontak: urut 01, 02, dst
    contact_names = [f"{contact_name} {i+1:02d}" for i in range(len(numbers))]

    # Nama file hasil sesuai input user, tanpa kode unik/timestamp
    output_name = f"{filename}.vcf"
    output_path = get_user_file_path(message.from_user.id, output_name)
    from utils.format import create_vcf_content, write_vcf_file
    vcf_content = create_vcf_content(contact_names, numbers)
    try:
        await write_vcf_file(output_path, vcf_content)
        await retry_send_document(message, output_path, output_name)
        log_bot(f"kirim file {output_name}")
        bot_msg = "File berhasil dikirim."
        await message.answer(bot_msg)
        log_bot(bot_msg)
    except Exception as e:
        err_msg = f"Gagal membuat/mengirim file: {e}"
        logging.error(err_msg)
        log_bot(err_msg)
        await message.answer(err_msg)
    finally:
        # Hapus file hasil setelah dikirim
        try:
            if os.path.exists(output_path):
                os.remove(output_path)
                logging.info(f"File hasil dihapus: {output_path}")
        except Exception as e:
            logging.error(f"Gagal hapus file hasil: {output_path} ({e})")
        await state.clear()