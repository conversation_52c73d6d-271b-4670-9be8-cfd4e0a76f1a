"""
Phone Number Converter Utilities
Konversi nomor telepon 08xxx ke 62xxx untuk format internasional Indonesia
"""

import re
import logging

def convert_08_to_62(phone_number):
    """
    Konversi nomor telepon 08xxx ke 62xxx HANYA di awal nomor.
    
    Rules:
    - <PERSON>ya konversi jika nomor dimulai dengan '08'
    - Tidak konversi jika '08' ada di tengah nomor
    - Preserve format lain (spasi, tanda hubung, dll) setelah konversi
    
    Args:
        phone_number (str): Nomor telepon input
        
    Returns:
        str: Nomor telepon yang sudah dikonversi
        
    Examples:
        convert_08_to_62("081234567890") → "6281234567890"
        convert_08_to_62("6281234567890") → "6281234567890" (tidak berubah)
        convert_08_to_62("12308567890") → "12308567890" (tidak berubah)
        convert_08_to_62("08 1234 5678") → "628 1234 5678"
        convert_08_to_62("+081234567890") → "+6281234567890"
    """
    if not phone_number or not isinstance(phone_number, str):
        return phone_number
    
    # Trim whitespace
    phone_number = phone_number.strip()
    
    if not phone_number:
        return phone_number
    
    # Case 1: Nomor dimulai dengan +08 (format internasional dengan +)
    if phone_number.startswith('+08'):
        converted = '+628' + phone_number[3:]  # +08 → +628 (ambil setelah +08)
        logging.debug(f"Converted +08 format: {phone_number} -> {converted}")
        return converted
    
    # Case 2: Nomor dimulai dengan 08 (format lokal)
    if phone_number.startswith('08'):
        converted = '628' + phone_number[2:]  # 08 → 628 (ambil setelah 08)
        logging.debug(f"Converted 08 format: {phone_number} -> {converted}")
        return converted
    
    # Case 3: Nomor tidak dimulai dengan 08, return as is
    return phone_number

def batch_convert_08_to_62(phone_numbers):
    """
    Konversi batch list nomor telepon 08xxx ke 62xxx.
    
    Args:
        phone_numbers (list): List nomor telepon
        
    Returns:
        tuple: (converted_numbers, conversion_count)
    """
    if not phone_numbers or not isinstance(phone_numbers, list):
        return phone_numbers, 0
    
    converted_numbers = []
    conversion_count = 0
    
    for phone_number in phone_numbers:
        original = phone_number
        converted = convert_08_to_62(phone_number)
        
        converted_numbers.append(converted)
        
        # Count conversions
        if original != converted:
            conversion_count += 1
    
    if conversion_count > 0:
        logging.info(f"Batch conversion: {conversion_count}/{len(phone_numbers)} numbers converted (08->62)")
    
    return converted_numbers, conversion_count

def clean_and_convert_phone_number(phone_number):
    """
    Clean nomor telepon dan konversi 08→62 sekaligus.
    Fungsi ini menggabungkan cleaning dan conversion dalam satu step.
    
    Args:
        phone_number (str): Nomor telepon input
        
    Returns:
        str: Nomor telepon yang sudah di-clean dan dikonversi
    """
    if not phone_number or not isinstance(phone_number, str):
        return phone_number
    
    # Step 1: Basic cleaning - hapus karakter non-digit kecuali + di awal
    cleaned = phone_number.strip()
    
    # Preserve + di awal jika ada
    has_plus = cleaned.startswith('+')
    
    # Extract hanya digit
    digits_only = re.sub(r'[^\d]', '', cleaned)
    
    # Reconstruct dengan + jika perlu
    if has_plus:
        cleaned = '+' + digits_only
    else:
        cleaned = digits_only
    
    # Step 2: Convert 08→62
    converted = convert_08_to_62(cleaned)
    
    return converted

def is_indonesian_number(phone_number):
    """
    Check apakah nomor telepon adalah nomor Indonesia.
    
    Args:
        phone_number (str): Nomor telepon
        
    Returns:
        bool: True jika nomor Indonesia
    """
    if not phone_number or not isinstance(phone_number, str):
        return False
    
    # Clean nomor dulu
    cleaned = re.sub(r'[^\d]', '', phone_number.strip())
    
    # Nomor Indonesia dimulai dengan 62 atau 08
    return cleaned.startswith('62') or cleaned.startswith('08')

def validate_converted_number(phone_number):
    """
    Validasi nomor telepon setelah konversi 08→62.
    
    Args:
        phone_number (str): Nomor telepon yang sudah dikonversi
        
    Returns:
        bool: True jika nomor valid
    """
    if not phone_number or not isinstance(phone_number, str):
        return False
    
    # Clean nomor untuk validasi
    cleaned = re.sub(r'[^\d]', '', phone_number.strip())
    
    # Nomor Indonesia harus:
    # 1. Dimulai dengan 62
    # 2. Panjang 10-15 digit
    # 3. Digit ketiga harus 8 (628xxx)
    if not cleaned.startswith('62'):
        return False
    
    if len(cleaned) < 10 or len(cleaned) > 15:
        return False
    
    if len(cleaned) >= 3 and cleaned[2] != '8':
        return False
    
    return True
