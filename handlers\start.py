from aiogram import Router, types, F
from aiogram.filters import Command
import logging
from aiogram.fsm.context import FSMContext
from config import TUTORIAL_LINK, ADMIN_USERNAMES, CONTACT_ADMIN

router = Router()

def log_user(message: types.Message):
    # logging.info(f"user: {message.text}")  # Disabled
    pass

def log_bot(text: str):
    # logging.info(f"bot: {text}")  # Disabled
    pass

fitur = [
    "/to_vcf      - konversi file ke .vcf",
    "/to_txt      - konversi file ke .txt",
    "/admin       - fitur admin/navy",
    "/manual      - input kontak manual",
    "/add         - tambah kontak ke .vcf",
    "/delete      - hapus kontak dari file",
    "/renamectc   - ganti nama kontak",
    "/renamefile  - ganti nama file",
    "/merge       - gabungkan file",
    "/split       - pecah file",
    "/count       - hitung jumlah kontak",
    "/nodup       - hapus kontak duplikat",
    "/getname     - extract nama file",
    "/generate    - generate nama file",
    "/setting     - menu pengaturan",
]

keyboard = types.ReplyKeyboardMarkup(
    keyboard=[
        [
            types.KeyboardButton(text="/to_vcf"),
            types.KeyboardButton(text="/to_txt"),
            types.KeyboardButton(text="/admin"),
            types.KeyboardButton(text="/manual"),
        ],
        [
            types.KeyboardButton(text="/add"),
            types.KeyboardButton(text="/delete"),
            types.KeyboardButton(text="/renamectc"),
            types.KeyboardButton(text="/renamefile"),
        ],
        [
            types.KeyboardButton(text="/merge"),
            types.KeyboardButton(text="/split"),
            types.KeyboardButton(text="/count"),
            types.KeyboardButton(text="/nodup"),
        ],
        [
            types.KeyboardButton(text="/getname"),
            types.KeyboardButton(text="/generate"),
            types.KeyboardButton(text="/setting"),
            types.KeyboardButton(text="/help"),
        ],
    ],
    resize_keyboard=True,
    one_time_keyboard=True
)

INFO_BOT = ""

# Tutorial inline keyboard - ukuran tombol disesuaikan dengan lebar deskripsi
tutorial_keyboard = types.InlineKeyboardMarkup(
    inline_keyboard=[
        [
            types.InlineKeyboardButton(
                text="💡 Tutorial Penggunaan Bot",
                url=TUTORIAL_LINK if TUTORIAL_LINK else "https://t.me/KazuhaID1"
            )
        ]
    ]
)

# Help/Support inline keyboard - tombol ke 2 admin (copy dari bot utama)
help_keyboard = types.InlineKeyboardMarkup(
    inline_keyboard=[
        [
            types.InlineKeyboardButton(
                text="👑 ADMIN 1",
                url="https://t.me/KazuhaID1"
            ),
            types.InlineKeyboardButton(
                text="👑 ADMIN 2",
                url="https://t.me/KazuhaID3"
            )
        ]
    ]
)



@router.message(Command("to_vcf"), F.chat.type == "private")
async def to_vcf_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.to_vcf import to_vcf_start
    await to_vcf_start(message, state)

@router.message(Command("to_txt"), F.chat.type == "private")
async def to_txt_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.to_txt import to_txt_start
    await to_txt_start(message, state)

@router.message(Command("help"), F.chat.type == "private")
async def help_global(message: types.Message, state: FSMContext):
    await state.clear()
    await help_handler(message, state)

@router.message(Command("admin"), F.chat.type == "private")
async def admin_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.admin import admin_start
    await admin_start(message, state)

@router.message(Command("manual"), F.chat.type == "private")
async def manual_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.manual import manual_start
    await manual_start(message, state)

@router.message(Command("add"), F.chat.type == "private")
async def add_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.add import add_start
    await add_start(message, state)

@router.message(Command("delete"), F.chat.type == "private")
async def delete_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.delete import delete_start
    await delete_start(message, state)

@router.message(Command("renamectc"), F.chat.type == "private")
async def renamectc_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.renamectc import renamectc_start
    await renamectc_start(message, state)

@router.message(Command("renamefile"), F.chat.type == "private")
async def renamefile_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.renamefile import renamefile_start
    await renamefile_start(message, state)

@router.message(Command("merge"), F.chat.type == "private")
async def merge_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.merge import merge_start
    await merge_start(message, state)

@router.message(Command("split"), F.chat.type == "private")
async def split_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.split import split_start
    await split_start(message, state)

@router.message(Command("count"), F.chat.type == "private")
async def count_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.count import count_start
    await count_start(message, state)

@router.message(Command("nodup"), F.chat.type == "private")
async def nodup_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.nodup import nodup_start
    await nodup_start(message, state)

@router.message(Command("getname"), F.chat.type == "private")
async def getname_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.getname import getname_start
    await getname_start(message, state)

@router.message(Command("generate"), F.chat.type == "private")
async def generate_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.generate import generate_start
    await generate_start(message, state)

@router.message(Command("setting"), F.chat.type == "private")
async def setting_from_start(message: types.Message, state: FSMContext):
    await state.clear()
    # Silent force release semaphore jika user stuck
    from utils.user_isolation import force_release_user_lock
    force_release_user_lock(message.from_user.id)
    from handlers.hide_menu import hide_menu_start
    await hide_menu_start(message, state)



@router.message(Command("cancel"), F.chat.type == "private")
async def cancel_from_start(message: types.Message, state: FSMContext):
    # Cancel handler akan dihandle oleh handlers.cancel
    # Ini hanya untuk memastikan /cancel muncul di daftar fitur
    from handlers.cancel import cancel_handler
    await cancel_handler(message, state)

@router.message(Command("start"), F.chat.type == "private")
async def start_handler(message: types.Message, state: FSMContext):
    nama = message.from_user.full_name or message.from_user.username or "pengguna"

    # Pesan pertama: deskripsi dengan keyboard menu utama
    description_msg = (
        f"Hallo *{nama}*, selamat datang di bot\n"
        f"*Fitur bot:*\n"
        "```\n" +
        "\n".join(fitur) +
        "\n```\n" +
        INFO_BOT
    )
    await message.answer(description_msg, parse_mode="Markdown", reply_markup=keyboard)

    # Pesan kedua: tombol tutorial
    await message.answer("💡 Tutorial penggunaan bot:", reply_markup=tutorial_keyboard)

    log_bot(description_msg)

@router.message(Command("help"), F.chat.type == "private")
async def help_handler(message: types.Message, state: FSMContext):
    log_user(message)

    # Pesan bantuan dan support
    help_msg = (
        "🛠️ <b>BUTUH BANTUAN?</b>\n\n"
        "• ❓ Tidak paham cara pakai?\n"
        "• ⚠️ Ada error atau bug?\n"
        "• 💬 Pertanyaan lainnya?\n\n"
        "<b>Lapor ke admin sekarang!</b>\n\n"
        "👇 <b>HUBUNGI ADMIN:</b>"
    )

    await message.answer(help_msg, parse_mode="HTML", reply_markup=help_keyboard)

    log_bot(help_msg)