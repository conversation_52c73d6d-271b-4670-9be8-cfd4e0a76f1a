import re

def _extract_last_number(s):
    """
    Ambil prefix dan angka terakhir dari string.
    Return (prefix, number, has_space_before_number, zero_format)
    Jika tidak ada angka, return (s, None, False, 0)
    """
    match = re.search(r'(.*?)(\d+)$', s)
    if match:
        prefix = match.group(1)
        number_str = match.group(2)
        number = int(number_str)
        # Cek apakah ada spasi sebelum angka
        has_space = prefix.endswith(' ')

        # Deteksi leading zero (hanya support 2 digit: 01-99)
        if number_str.startswith('0') and len(number_str) == 2:
            zero_format = 2  # Leading zero 2 digit
        else:
            zero_format = 0  # Natural number atau 3+ digit (treated as natural)

        return prefix, number, has_space, zero_format
    return s, None, False, 0

def generate_file_names(base_name, file_count, part_counts=None, split_mode="all", naming_mode="beruntun"):
    """
    Generate file names sesuai 13 kondisi penamaan dengan leading zero support.
    - base_name: nama file dari user
    - file_count: jumlah file input user
    - part_counts: list jumlah part per file (jika split), contoh: [3,2] artinya file 1 dipecah 3, file 2 dipecah 2
    - split_mode: "all" jika user pilih semua, atau int jika split per N kontak
    - naming_mode: "beruntun" atau "per_file" (hanya berlaku untuk multiple files + split)
    """
    prefix, last_number, has_space, zero_format = _extract_last_number(base_name)
    names = []

    def format_number(n):
        """Helper function untuk format angka sesuai zero_format"""
        if zero_format == 2:
            return f"{n:02d}"  # Leading zero 2 digit: 01, 02, 03
        else:
            return str(n)  # Natural: 1, 2, 3

    # 1 file, semua kontak, nama file persis user
    if file_count == 1 and split_mode == "all":
        names.append(base_name)
        return names

    # Lebih dari 1 file, semua kontak, nama file diberi angka lanjut (dari last_number jika ada)
    if file_count > 1 and split_mode == "all":
        start = last_number if last_number is not None else 0
        for i in range(file_count):
            n = start + i
            if last_number is not None:
                # Pertahankan format spasi asli user dan format angka
                formatted_n = format_number(n)
                if has_space:
                    names.append(f"{prefix}{formatted_n}")  # prefix sudah ada spasi
                else:
                    names.append(f"{prefix}{formatted_n}")  # prefix tanpa spasi
            else:
                # Tambah spasi sebelum angka, mulai dari 1
                names.append(f"{base_name} {i+1}")
        return names

    # 1 file, split per N kontak, nama file diberi angka lanjut (dari last_number jika ada)
    if file_count == 1 and isinstance(split_mode, int):
        part_total = part_counts[0] if part_counts else 1
        start = last_number if last_number is not None else 0
        for i in range(part_total):
            n = start + i
            if last_number is not None:
                # Pertahankan format spasi asli user dan format angka
                formatted_n = format_number(n)
                if has_space:
                    names.append(f"{prefix}{formatted_n}")  # prefix sudah ada spasi
                else:
                    names.append(f"{prefix}{formatted_n}")  # prefix tanpa spasi
            else:
                names.append(f"{base_name} {i+1}")
        return names

    # Lebih dari 1 file, split per N kontak
    if file_count > 1 and isinstance(split_mode, int):
        if naming_mode == "beruntun":
            # Mode beruntun: semua file hasil pakai 1 base name, angka lanjut berurutan
            start = last_number if last_number is not None else 0
            total_parts = sum(part_counts) if part_counts else file_count
            for i in range(total_parts):
                n = start + i
                if last_number is not None:
                    # Pertahankan format spasi asli user dan format angka
                    formatted_n = format_number(n)
                    if has_space:
                        names.append(f"{prefix}{formatted_n}")  # prefix sudah ada spasi
                    else:
                        names.append(f"{prefix}{formatted_n}")  # prefix tanpa spasi
                else:
                    names.append(f"{base_name} {i+1}")
            return names
        else:
            # Mode per_file: setiap file asli jadi prefix dengan suffix _1, _2, dst
            start = last_number if last_number is not None else 0
            for file_idx in range(file_count):
                file_number = start + file_idx
                part_total = part_counts[file_idx] if part_counts else 1
                for part_idx in range(part_total):
                    part_number = part_idx + 1
                    if last_number is not None:
                        # Pertahankan format spasi asli user untuk base, tambah underscore
                        formatted_file_number = format_number(file_number)
                        if has_space:
                            # Hapus spasi di akhir prefix untuk underscore
                            clean_prefix = prefix.rstrip()
                            names.append(f"{clean_prefix}{formatted_file_number}_{part_number}")
                        else:
                            names.append(f"{prefix}{formatted_file_number}_{part_number}")
                    else:
                        names.append(f"{base_name}{file_idx+1}_{part_number}")
            return names

    # Default fallback
    names.append(base_name)
    return names