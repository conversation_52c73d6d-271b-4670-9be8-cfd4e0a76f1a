"""
Safe File Cleanup - Handle file deletion dengan retry dan delay
"""

import os
import asyncio
import logging
import time
from typing import List

async def safe_remove_file(file_path: str, max_retries: int = 3, delay_seconds: float = 1.0) -> bool:
    """
    Safely remove file dengan retry mechanism
    
    Args:
        file_path: Path ke file yang akan dihapus
        max_retries: Maximum retry attempts
        delay_seconds: Delay antar retry
        
    Returns:
        bool: True jika berhasil dihapus
    """
    if not os.path.exists(file_path):
        return True  # File sudah tidak ada
    
    for attempt in range(max_retries):
        try:
            os.remove(file_path)
            # logging.info(f"File berhasil dihapus: {os.path.basename(file_path)}")  # Reduced logging
            return True
        except PermissionError as e:
            if attempt < max_retries - 1:
                # logging.warning(f"File masih digunakan, retry {attempt + 1}/{max_retries}: {os.path.basename(file_path)}")  # Reduced logging
                await asyncio.sleep(delay_seconds)
                continue
            else:
                # Silent fail pada attempt terakhir - tidak perlu log error
                # logging.error(f"Gagal hapus file setelah {max_retries} attempts: {file_path} ({e})")
                return False
        except Exception as e:
            # logging.error(f"Error menghapus file {file_path}: {e}")  # Reduced logging
            return False
    
    return False

async def safe_remove_files(file_paths: List[str], max_retries: int = 3, delay_seconds: float = 1.0) -> tuple:
    """
    Safely remove multiple files dengan retry mechanism
    
    Args:
        file_paths: List path ke files yang akan dihapus
        max_retries: Maximum retry attempts per file
        delay_seconds: Delay antar retry
        
    Returns:
        tuple: (success_count, failed_count, failed_files)
    """
    if not file_paths:
        return 0, 0, []
    
    success_count = 0
    failed_count = 0
    failed_files = []
    
    # Process files dengan delay untuk menghindari conflict
    for i, file_path in enumerate(file_paths):
        if i > 0:
            await asyncio.sleep(0.1)  # Small delay antar file
        
        success = await safe_remove_file(file_path, max_retries, delay_seconds)
        
        if success:
            success_count += 1
        else:
            failed_count += 1
            failed_files.append(os.path.basename(file_path))
    
    # Log summary hanya jika ada yang gagal
    if failed_count > 0:
        # logging.warning(f"File cleanup: {success_count} berhasil, {failed_count} gagal")  # Reduced logging
        pass
    
    return success_count, failed_count, failed_files

async def delayed_file_cleanup(file_paths: List[str], delay_before_cleanup: float = 2.0) -> tuple:
    """
    Cleanup files dengan delay awal untuk memastikan proses lain sudah selesai
    
    Args:
        file_paths: List path ke files yang akan dihapus
        delay_before_cleanup: Delay sebelum mulai cleanup
        
    Returns:
        tuple: (success_count, failed_count, failed_files)
    """
    if not file_paths:
        return 0, 0, []
    
    # Wait untuk memastikan file tidak lagi digunakan
    await asyncio.sleep(delay_before_cleanup)
    
    # Cleanup dengan retry
    return await safe_remove_files(file_paths, max_retries=5, delay_seconds=1.5)

def schedule_background_cleanup(file_paths: List[str], delay_minutes: int = 5):
    """
    Schedule background cleanup untuk file yang gagal dihapus
    
    Args:
        file_paths: List path ke files yang akan dihapus
        delay_minutes: Delay dalam menit sebelum cleanup
    """
    if not file_paths:
        return
    
    async def background_cleanup():
        await asyncio.sleep(delay_minutes * 60)  # Convert to seconds
        success_count, failed_count, failed_files = await safe_remove_files(file_paths, max_retries=10, delay_seconds=2.0)
        
        if success_count > 0:
            logging.info(f"Background cleanup: {success_count} files berhasil dihapus")
        
        if failed_count > 0:
            logging.warning(f"Background cleanup: {failed_count} files masih gagal dihapus: {failed_files}")
    
    # Schedule background task
    try:
        asyncio.create_task(background_cleanup())
    except Exception as e:
        logging.error(f"Error scheduling background cleanup: {e}")

# Legacy function untuk backward compatibility
async def remove_file(path: str):
    """
    Legacy function - gunakan safe_remove_file() untuk implementasi baru
    """
    return await safe_remove_file(path, max_retries=3, delay_seconds=1.0)
